# PlayBeg DJ Onboarding Journey Design

## Executive Summary

This document outlines a comprehensive DJ onboarding experience that guides new users from account signup through their first session creation. The design emphasizes progressive disclosure, flexible music service integration, and mobile-responsive interfaces while building on the existing Convex-based architecture.

## Current State Analysis

### Existing Implementation
- ✅ Basic DJ profile creation via `djProfiles.ts`
- ✅ Onboarding completion tracking (`completedOnboarding` field)
- ✅ Apple Music integration with MusicKit
- ✅ Spotify integration for song search and validation
- ✅ Session creation workflow
- ✅ Mobile-responsive design patterns

### Gaps Identified
- ❌ No structured onboarding flow after signup
- ❌ No music service selection interface
- ❌ No guided first session creation
- ❌ No progressive feature introduction
- ❌ Single-service enforcement needed

## 1. Post-Signup Onboarding Flow (Simplified)

### Flow Overview
```
Account Signup → Welcome Screen → Profile Setup → Music Service Selection →
Session Defaults & First Session → Onboarding Complete
```

**Key Changes:**
- **4 Steps Total** (reduced from 6)
- **Single Service Choice** (no multi-service support)
- **Combined Session Setup** (preferences + first session merged)
- **State Preservation** (all skip buttons preserve entered data)
- **Simplified Service Cards** (minimal feature comparison)

### Step 1: Welcome & Orientation Screen

**Purpose**: Orient new DJs and set expectations
**Duration**: 30-45 seconds
**Skip Option**: Yes (preserves any existing profile data)

#### Standardized Progress Indicator
All onboarding screens now use a horizontal progress bar with step labels:

```
┌─────────────────────────────────────────────────────────────┐
│ Welcome        Profile        Music Service    Session Setup │
│ ████████████   ░░░░░░░░░░░░   ░░░░░░░░░░░░░   ░░░░░░░░░░░░░ │
│ Step 1 of 4                                                 │
└─────────────────────────────────────────────────────────────┘
```

#### Desktop Layout (1200px+)
```
┌─────────────────────────────────────────────────────────────┐
│ Welcome        Profile        Music Service    Session Setup │
│ ████████████   ░░░░░░░░░░░░   ░░░░░░░░░░░░░   ░░░░░░░░░░░░░ │
│ Step 1 of 4                                       [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│           🎧 Welcome to PlayBeg, [DJ Name]!                │
│                                                             │
│    Transform how you connect with your audience            │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │     📱      │  │     🎵      │  │     📊      │        │
│  │ QR Requests │  │ Real-time   │  │ Analytics   │        │
│  │             │  │ Management  │  │ & Insights  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│              [Let's Get Started] [Watch Demo]              │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout (320px-768px)
```
┌─────────────────────────────┐
│ Welcome    Profile    Music │
│ ████████   ░░░░░░░░   ░░░░░ │
│ Step 1 of 4           [Skip]│
├─────────────────────────────┤
│                             │
│    🎧 Welcome to PlayBeg!   │
│                             │
│  Transform how you connect  │
│     with your audience      │
│                             │
│        ┌─────────────┐      │
│        │     📱      │      │
│        │ QR Requests │      │
│        └─────────────┘      │
│                             │
│        ┌─────────────┐      │
│        │     🎵      │      │
│        │ Real-time   │      │
│        │ Management  │      │
│        └─────────────┘      │
│                             │
│    [Let's Get Started]      │
│       [Watch Demo]          │
│                             │
└─────────────────────────────┘
```

#### Component Specifications
- **Progress Bar**: Horizontal bar with step labels (standardized across all steps)
- **Skip Button**: Preserves any existing profile data, marks step as skipped
- **Hero Section**: Animated DJ name insertion with typewriter effect
- **Feature Cards**: Hover animations on desktop, tap animations on mobile
- **CTA Buttons**: Primary action (Let's Get Started), Secondary (Watch Demo)
- **Animations**: Fade-in sequence for feature cards (300ms stagger)

#### State Preservation
- Skip button saves `{ step: 1, skipped: true, timestamp: Date.now() }` to onboarding progress
- Any existing profile data remains intact
- User can return to complete this step later

### Step 2: DJ Profile Setup

**Purpose**: Collect essential DJ information and preferences
**Duration**: 2-3 minutes
**Skip Option**: Partial (can complete later)

#### Form Fields & Validation

**Required Fields:**
1. **DJ Display Name**
   - Input type: Text
   - Validation: 2-50 characters, no special characters except spaces, hyphens, underscores
   - Error messages: "Display name must be 2-50 characters" / "Only letters, numbers, spaces, hyphens, and underscores allowed"
   - Real-time validation with debounced checking

2. **Primary Venue Type** (Single select)
   - Options: Nightclub, Wedding Venue, Corporate Events, Private Parties, Mobile DJ, Other
   - Default: None selected
   - Validation: Required selection
   - Error message: "Please select your primary venue type"

**Optional Fields:**
3. **Profile Picture**
   - Input type: File upload (drag & drop + click to browse)
   - Accepted formats: JPG, PNG, WebP
   - Max size: 5MB
   - Auto-resize to 400x400px
   - Validation: File type and size checking
   - Error messages: "Please upload a JPG, PNG, or WebP image" / "Image must be under 5MB"

4. **Bio/Description**
   - Input type: Textarea
   - Character limit: 500 characters
   - Placeholder: "Tell your audience about your style and experience..."
   - Validation: Max 500 characters
   - Character counter with color coding (green < 400, yellow 400-480, red > 480)

5. **Years of Experience**
   - Input type: Select dropdown
   - Options: New to DJing, 1-2 years, 3-5 years, 6-10 years, 10+ years
   - Default: None selected
   - Optional field

6. **Location** (City, State/Country)
   - Input type: Text with autocomplete
   - Validation: Optional, 2-100 characters if provided
   - Placeholder: "e.g., Los Angeles, CA"

#### Desktop Layout
```
┌─────────────────────────────────────────────────────────────┐
│ Welcome        Profile        Music Service    Session Setup │
│ ░░░░░░░░░░░░   ████████████   ░░░░░░░░░░░░░   ░░░░░░░░░░░░░ │
│ [← Back]      Step 2 of 4                           [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐                                           │
│  │             │  DJ Display Name *                        │
│  │   Upload    │  ┌─────────────────────────────────────┐  │
│  │   Photo     │  │ [Input Field]                       │  │
│  │             │  └─────────────────────────────────────┘  │
│  │  [Browse]   │                                           │
│  └─────────────┘  Primary Venue Type *                     │
│                   ┌─────────────────────────────────────┐  │
│                   │ [Dropdown: Select venue type...]   │  │
│                   └─────────────────────────────────────┘  │
│                                                             │
│  Bio/Description (Optional)                                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │ [Textarea - 500 char limit]                        │  │
│  │                                                     │  │
│  └─────────────────────────────────────────────────────┘  │
│  Characters: 0/500                                         │
│                                                             │
│  Years of Experience        Location                       │
│  ┌─────────────────────┐    ┌─────────────────────────┐    │
│  │ [Select...]         │    │ [City, State/Country]   │    │
│  └─────────────────────┘    └─────────────────────────┘    │
│                                                             │
│                    [Save & Continue]                       │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout
```
┌─────────────────────────────┐
│ Welcome    Profile    Music │
│ ░░░░░░░░   ████████   ░░░░░ │
│ [← Back]  Step 2 of 4 [Skip]│
├─────────────────────────────┤
│                             │
│      ┌─────────────┐        │
│      │             │        │
│      │   Upload    │        │
│      │   Photo     │        │
│      │             │        │
│      │  [Browse]   │        │
│      └─────────────┘        │
│                             │
│ DJ Display Name *           │
│ ┌─────────────────────────┐ │
│ │ [Input Field]           │ │
│ └─────────────────────────┘ │
│                             │
│ Primary Venue Type *        │
│ ┌─────────────────────────┐ │
│ │ [Select venue type...]  │ │
│ └─────────────────────────┘ │
│                             │
│ Bio (Optional)              │
│ ┌─────────────────────────┐ │
│ │ [Textarea]              │ │
│ │                         │ │
│ └─────────────────────────┘ │
│ Characters: 0/500           │
│                             │
│ Experience                  │
│ ┌─────────────────────────┐ │
│ │ [Select...]             │ │
│ └─────────────────────────┘ │
│                             │
│ Location                    │
│ ┌─────────────────────────┐ │
│ │ [City, State/Country]   │ │
│ └─────────────────────────┘ │
│                             │
│    [Save & Continue]        │
│                             │
└─────────────────────────────┘
```

#### Component Specifications
- **File Upload**: Drag & drop zone with preview, progress indicator, error states
- **Form Validation**: Real-time validation with inline error messages
- **Character Counter**: Dynamic color coding and real-time updates
- **Auto-save**: Saves draft every 30 seconds to prevent data loss
- **Skip Preservation**: Skip button saves all entered data before proceeding
- **Accessibility**: Full keyboard navigation, screen reader support, ARIA labels

#### State Preservation on Skip
```typescript
const handleSkip = async () => {
  // Save current form data before skipping
  const currentData = {
    displayName: formData.displayName,
    venueType: formData.venueType,
    bio: formData.bio,
    experience: formData.experience,
    location: formData.location,
    profilePicture: uploadedFile, // Preserve uploaded file
  };

  await saveOnboardingProgress({
    step: 2,
    data: currentData,
    skipped: true,
    timestamp: Date.now(),
  });

  // Proceed to next step
  setCurrentStep(3);
};
```

#### API Integration
```typescript
// Enhanced mutation with state preservation
const updateProfile = useMutation(api.djProfiles.updateDjProfile);
const saveProgress = useMutation(api.onboarding.saveOnboardingProgress);

const handleSave = async (formData: ProfileFormData) => {
  try {
    // Save to profile
    await updateProfile({
      profileId: djProfile._id,
      displayName: formData.displayName,
      // ... other fields
    });

    // Mark step as complete
    await saveProgress({
      step: 2,
      data: formData,
      completed: true,
    });

    // Proceed to next step
    setCurrentStep(3);
  } catch (error) {
    // Handle validation errors
    setErrors(error.validationErrors);
  }
};
```

### Step 3: Music Service Selection (Simplified)

**Purpose**: Choose ONE music streaming service for song search and playlist management
**Duration**: 30 seconds - 1 minute
**Skip Option**: Yes (enables manual entry mode, can configure later)

#### Simplified Service Selection

**Single Service Choice** (enforced - no multi-service support):

1. **Apple Music**
   - Full playlist integration
   - Automatic song adding
   - Requires Apple Music subscription

2. **Spotify**
   - Song search and validation
   - Manual playlist management
   - Works with free or premium accounts

3. **Skip for Now**
   - Manual song entry only
   - Can connect a service later in settings

#### Desktop Layout (Simplified)
```
┌─────────────────────────────────────────────────────────────┐
│ Welcome        Profile        Music Service    Session Setup │
│ ░░░░░░░░░░░░   ░░░░░░░░░░░░   ████████████   ░░░░░░░░░░░░░ │
│ [← Back]      Step 3 of 4                           [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│           Choose your music service                         │
│                                                             │
│  ┌─────────────────────────────┐  ┌─────────────────────────────┐ │
│  │        🍎 Apple Music       │  │         🎵 Spotify          │ │
│  │                             │  │                             │ │
│  │ • Full playlist integration │  │ • Song search & validation  │ │
│  │ • Automatic song adding     │  │ • Works with free accounts  │ │
│  │                             │  │                             │ │
│  │ Requires Apple Music        │  │ Requires Spotify account    │ │
│  │ subscription                │  │ (free or premium)           │ │
│  │                             │  │                             │ │
│  │     [Connect Apple]         │  │     [Connect Spotify]       │ │
│  └─────────────────────────────┘  └─────────────────────────────┘ │
│                                                             │
│                    [Learn More About Services]             │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                ⏭️ Skip for Now                      │   │
│  │                                                     │   │
│  │ Use manual song entry (can connect a service later) │   │
│  │                                                     │   │
│  │              [Continue with Manual Entry]           │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout (Simplified Stacked Cards)
```
┌─────────────────────────────┐
│ Welcome    Profile    Music │
│ ░░░░░░░░   ░░░░░░░░   █████ │
│ [← Back]  Step 3 of 4 [Skip]│
├─────────────────────────────┤
│                             │
│   Choose your music service │
│                             │
│ ┌─────────────────────────┐ │
│ │    🍎 Apple Music       │ │
│ │                         │ │
│ │ • Full integration      │ │
│ │ • Automatic adding      │ │
│ │                         │ │
│ │ Requires Apple Music    │ │
│ │ subscription            │ │
│ │                         │ │
│ │   [Connect Apple]       │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │   🎵 Spotify            │ │
│ │                         │ │
│ │ • Song search           │ │
│ │ • Works with free       │ │
│ │                         │ │
│ │ Requires Spotify        │ │
│ │ account (free/premium)  │ │
│ │                         │ │
│ │  [Connect Spotify]      │ │
│ └─────────────────────────┘ │
│                             │
│    [Learn More]             │
│                             │
│ ┌─────────────────────────┐ │
│ │   ⏭️ Skip for Now       │ │
│ │                         │ │
│ │ Manual entry only       │ │
│ │ (connect later)         │ │
│ │                         │ │
│ │  [Continue Manual]      │ │
│ └─────────────────────────┘ │
│                             │
└─────────────────────────────┘
```

#### Simplified Service Connection Flows

**Apple Music Connection:**
1. Click "Connect Apple" → MusicKit authorization popup
2. User grants permission → Token stored securely
3. Connection verified → Success state with checkmark
4. Auto-advance to next step

**Spotify Connection:**
1. Click "Connect Spotify" → OAuth redirect to Spotify
2. User authorizes PlayBeg → Redirect back with auth code
3. Token exchange and storage → Success confirmation
4. Auto-advance to next step

**Skip for Now:**
1. Click "Continue with Manual Entry" → Saves skip state
2. Preserves any previous profile data
3. Sets music service to "manual" mode
4. Auto-advance to next step

#### Component Specifications
- **Simplified Cards**: Two main service options + skip option
- **Learn More Link**: Detailed comparison hidden behind link to reduce cognitive load
- **Connection States**: Loading, success, error with retry options
- **Single Service Enforcement**: Prevents connecting multiple services
- **Skip Preservation**: Maintains all previously entered data
- **Error Handling**: Clear error messages with troubleshooting links
- **Security**: OAuth flows with secure token storage

#### State Preservation on Skip
```typescript
const handleSkip = async () => {
  // Preserve all previous data and set manual mode
  await saveOnboardingProgress({
    step: 3,
    data: {
      musicService: 'manual',
      skippedMusicSetup: true,
    },
    skipped: true,
    timestamp: Date.now(),
  });

  // Proceed to session setup
  setCurrentStep(4);
};
```

### Step 4: Session Defaults & First Session (Combined)

**Purpose**: Set session defaults and create first session in one streamlined step
**Duration**: 2-3 minutes
**Skip Option**: Yes (uses sensible defaults, can create session later)

#### Desktop Layout (Combined Step)
```
┌─────────────────────────────────────────────────────────────┐
│ Welcome        Profile        Music Service    Session Setup │
│ ░░░░░░░░░░░░   ░░░░░░░░░░░░   ░░░░░░░░░░░░░   ████████████ │
│ [← Back]      Step 4 of 4                           [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Create Your First Session                                  │
│                                                             │
│  Session Information                                        │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Session Name *                                      │   │
│  │ ┌─────────────────────────────────────────────────┐ │   │
│  │ │ My First PlayBeg Session                        │ │   │
│  │ └─────────────────────────────────────────────────┘ │   │
│  │                                                     │   │
│  │ Event Type *                                        │   │
│  │ ┌─────────────────────────────────────────────────┐ │   │
│  │ │ Practice Session ▼                              │ │   │
│  │ └─────────────────────────────────────────────────┘ │   │
│  │                                                     │   │
│  │ Duration: ● 4 hours  ○ 2 hours  ○ 6 hours         │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Music Service: Apple Music ✓ (Connected)                  │
│                                                             │
│  ▼ Advanced Settings (Optional)                            │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Request Limit per Person: ● 3  ○ 5  ○ 10  ○ Unlimited │ │
│  │ Request Handling: ● Ask each time  ○ Auto-accept    │   │
│  │ Genre Blocking: ☐ Explicit  ☐ Heavy Metal  ☐ Country │ │
│  │ Notifications: ● Immediate  ○ Batched  ○ Off        │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│                  [Create Session & Finish]                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout (Combined Step)
```
┌─────────────────────────────┐
│ Welcome    Profile    Music │
│ ░░░░░░░░   ░░░░░░░░   █████ │
│ [← Back]  Step 4 of 4 [Skip]│
├─────────────────────────────┤
│                             │
│ Create Your First Session   │
│                             │
│ Session Information         │
│ ┌─────────────────────────┐ │
│ │ Session Name *          │ │
│ │ ┌─────────────────────┐ │ │
│ │ │ My First PlayBeg    │ │ │
│ │ │ Session             │ │ │
│ │ └─────────────────────┘ │ │
│ │                         │ │
│ │ Event Type *            │ │
│ │ ┌─────────────────────┐ │ │
│ │ │ Practice Session ▼  │ │ │
│ │ └─────────────────────┘ │ │
│ │                         │ │
│ │ Duration                │ │
│ │ ● 4h  ○ 2h  ○ 6h       │ │
│ └─────────────────────────┘ │
│                             │
│ Music: Apple Music ✓        │
│                             │
│ ▼ Advanced Settings         │
│ ┌─────────────────────────┐ │
│ │ Request Limit: ● 3      │ │
│ │ Handling: ● Ask each    │ │
│ │ Notifications: ● On     │ │
│ │ ☐ Block explicit        │ │
│ └─────────────────────────┘ │
│                             │
│  [Create Session & Finish] │
│                             │
└─────────────────────────────┘
```

#### Component Specifications (Combined Step)
- **Session Creation**: Essential fields only (name, type, duration)
- **Advanced Settings**: Collapsed by default to reduce cognitive load
- **Music Service Display**: Shows connected service (read-only)
- **Smart Defaults**: Uses sensible defaults for all optional settings
- **Skip Preservation**: Saves session defaults even if session creation is skipped
- **Progressive Disclosure**: Advanced settings hidden behind toggle

#### State Preservation on Skip
```typescript
const handleSkip = async () => {
  // Save session defaults even if skipping session creation
  const sessionDefaults = {
    defaultDuration: 4, // hours
    defaultRequestLimit: 3,
    defaultRequestHandling: 'ask',
    defaultNotifications: 'immediate',
    defaultGenreBlocks: [],
  };

  await saveOnboardingProgress({
    step: 4,
    data: {
      sessionDefaults,
      skippedSessionCreation: true,
    },
    skipped: true,
    timestamp: Date.now(),
  });

  // Mark onboarding as complete
  await completeOnboarding();

  // Redirect to dashboard
  router.push('/dashboard');
};
```

#### API Integration (Combined Step)
```typescript
const createSessionAndComplete = async (sessionData: SessionFormData) => {
  try {
    // Create the session
    const session = await createSession({
      name: sessionData.name,
      eventType: sessionData.eventType,
      duration: sessionData.duration,
      musicService: connectedService, // From step 3
      // Advanced settings with defaults
      requestLimit: sessionData.requestLimit || 3,
      requestHandling: sessionData.requestHandling || 'ask',
      genreBlocks: sessionData.genreBlocks || [],
    });

    // Save session defaults for future use
    await updateDjProfile({
      profileId: djProfile._id,
      defaultSessionDuration: sessionData.duration,
      defaultRequestLimit: sessionData.requestLimit,
      defaultRequestHandling: sessionData.requestHandling,
      defaultGenreBlocks: sessionData.genreBlocks,
    });

    // Mark onboarding as complete
    await completeOnboarding();

    // Redirect to session success page
    router.push(`/session/${session._id}/success`);
  } catch (error) {
    setErrors(error.validationErrors);
  }
};
```

### Onboarding Complete & Next Steps

**Purpose**: Celebrate completion and guide toward first actions
**Duration**: 30 seconds
**Skip Option**: No (completion screen)

#### Success Screen Layout

**Desktop:**
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                        🎉 Congratulations!                 │
│                                                             │
│              Your DJ profile is ready to rock!             │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                Session Created ✓                    │   │
│  │                                                     │   │
│  │        "My First PlayBeg Session"                   │   │
│  │                                                     │   │
│  │    🔗 playbeg.com/session/abc123                   │   │
│  │                                                     │   │
│  │              [Copy Link] [Show QR]                 │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│                      What's Next?                          │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │     📱      │  │     🎵      │  │     📚      │        │
│  │ Share Your  │  │ Test Song   │  │ Learn More  │        │
│  │ QR Code     │  │ Requests    │  │ Features    │        │
│  │             │  │             │  │             │        │
│  │ [Share]     │  │ [Test]      │  │ [Guide]     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│                  [Go to Dashboard]                         │
│                                                             │
│                       ● ● ● ● ●                           │
└─────────────────────────────────────────────────────────────┘
```

**Mobile:**
```
┌─────────────────────────────┐
│                             │
│      🎉 Congratulations!    │
│                             │
│   Your DJ profile is ready  │
│        to rock!             │
│                             │
│ ┌─────────────────────────┐ │
│ │   Session Created ✓     │ │
│ │                         │ │
│ │ "My First PlayBeg       │ │
│ │  Session"               │ │
│ │                         │ │
│ │ 🔗 playbeg.com/session/ │ │
│ │    abc123               │ │
│ │                         │ │
│ │   [Copy] [Show QR]      │ │
│ └─────────────────────────┘ │
│                             │
│      What's Next?           │
│                             │
│ ┌─────────────────────────┐ │
│ │     📱 Share QR         │ │
│ │     [Share]             │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │     🎵 Test Requests    │ │
│ │     [Test]              │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │     📚 Learn More       │ │
│ │     [Guide]             │ │
│ └─────────────────────────┘ │
│                             │
│    [Go to Dashboard]        │
│                             │
│        ● ● ● ● ●            │
└─────────────────────────────┘
```

#### Component Specifications
- **Success Animation**: Confetti or celebration animation on load
- **Session Summary**: Display created session details with quick actions
- **Next Steps Cards**: Interactive cards with hover/tap animations
- **Quick Actions**: Copy link, show QR code, test functionality
- **Navigation**: Clear path to dashboard with session management

#### API Integration
```typescript
// Mark onboarding as complete
const completeOnboarding = useMutation(api.djProfiles.completeOnboarding);

const handleComplete = async () => {
  try {
    await completeOnboarding({
      displayName: profileData.displayName,
    });

    // Redirect to dashboard
    router.push('/dashboard');
  } catch (error) {
    console.error('Failed to complete onboarding:', error);
  }
};
```

## 2. Music Service Integration Strategy (Simplified)

### Single Service Architecture

The simplified music service integration enforces single service choice:
1. **One Service Only**: DJs choose exactly one service during onboarding
2. **No Multi-Service Support**: Cannot connect both Apple Music and Spotify
3. **Service Switching**: Can disconnect and connect a different service later
4. **Session Consistency**: All sessions use the same connected service

### Simplified Service Capabilities

| Feature | Apple Music | Spotify | Manual Entry |
|---------|-------------|---------|--------------|
| Song Search | ✅ Full | ✅ Full | ❌ Manual |
| Playlist Integration | ✅ Automatic | ⚠️ Limited | ❌ None |
| Auto-Add Songs | ✅ Yes | ❌ No | ❌ No |
| Metadata Validation | ✅ Complete | ✅ Complete | ⚠️ Limited |

### Simplified Service Configuration
```typescript
interface SimplifiedMusicServiceConfig {
  // Single service only - no multi-service support
  connectedService: 'apple' | 'spotify' | 'manual';
  serviceData?: {
    // Apple Music data
    appleMusicToken?: string;
    appleMusicLastSync?: number;
    // OR Spotify data (mutually exclusive)
    spotifyAccessToken?: string;
    spotifyRefreshToken?: string;
    spotifyLastSync?: number;
  };
  // No fallback or multi-service preferences needed
}
```

#### Session Service Display (Read-Only)

Since DJs can only connect one service, session creation shows the connected service:

**Desktop Interface:**
```
┌─────────────────────────────────────────────────────────────┐
│ Music Service for this Session                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  🍎 Apple Music (Connected)                                │
│  Full playlist integration, auto-add approved songs        │
│                                                             │
│  [Change Music Service in Settings]                        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**Mobile Interface:**
```
┌─────────────────────────────┐
│ Music Service               │
├─────────────────────────────┤
│                             │
│ 🍎 Apple Music ✓            │
│ Full integration            │
│                             │
│ [Change in Settings]        │
│                             │
└─────────────────────────────┘
```

#### Service Switching (Post-Onboarding)

DJs can change their music service in settings, but must disconnect the current service first:

```typescript
const switchMusicService = async (newService: 'apple' | 'spotify') => {
  // Must disconnect current service first
  await disconnectCurrentService();

  // Then connect new service
  if (newService === 'apple') {
    await connectAppleMusic();
  } else {
    await connectSpotify();
  }

  // Update DJ profile
  await updateDjProfile({
    profileId: djProfile._id,
    connectedService: newService,
  });
};
```

### Service Connection Workflows

#### Apple Music Connection Flow
```typescript
// Component: AppleMusicConnector
const connectAppleMusic = async () => {
  setConnectionState('connecting');

  try {
    // Initialize MusicKit if not already done
    await musicService.initialize();

    // Request user authorization
    const success = await musicService.authorize();

    if (success) {
      // Store connection in Convex
      await updateDjProfile({
        profileId: djProfile._id,
        appleMusicConnected: true,
        appleMusicToken: musicService.getUserToken(),
      });

      setConnectionState('connected');
      onConnectionSuccess('apple');
    } else {
      throw new Error('Authorization failed');
    }
  } catch (error) {
    setConnectionState('error');
    setError(error.message);
  }
};
```

#### Spotify Connection Flow
```typescript
// Component: SpotifyConnector
const connectSpotify = async () => {
  setConnectionState('connecting');

  try {
    // Redirect to Spotify OAuth
    const authUrl = `https://accounts.spotify.com/authorize?` +
      `client_id=${SPOTIFY_CLIENT_ID}&` +
      `response_type=code&` +
      `redirect_uri=${REDIRECT_URI}&` +
      `scope=user-read-private user-read-email playlist-modify-public`;

    window.location.href = authUrl;
  } catch (error) {
    setConnectionState('error');
    setError(error.message);
  }
};

// Handle OAuth callback
const handleSpotifyCallback = async (code: string) => {
  try {
    // Exchange code for tokens
    const tokenResponse = await fetch('/api/spotify/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code }),
    });

    const tokens = await tokenResponse.json();

    // Store in Convex
    await updateDjProfile({
      profileId: djProfile._id,
      spotifyConnected: true,
      spotifyTokens: tokens,
    });

    setConnectionState('connected');
    onConnectionSuccess('spotify');
  } catch (error) {
    setConnectionState('error');
    setError(error.message);
  }
};
```

### Service Switching & Management

#### Settings Interface for Service Management
```
┌─────────────────────────────────────────────────────────────┐
│ Music Service Settings                                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Connected Services                                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ 🍎 Apple Music                              ✅ Connected │
│  │ Last synced: 2 hours ago                              │
│  │ [Disconnect] [Test Connection] [Refresh Token]       │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ 🎵 Spotify                                  ✅ Connected │
│  │ Last synced: 5 minutes ago                            │
│  │ [Disconnect] [Test Connection] [Refresh Token]       │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Default Preferences                                        │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Primary Service: ● Apple Music ○ Spotify ○ Ask      │
│  │ Fallback Service: ○ Apple Music ● Spotify ○ Manual  │
│  │ Auto-sync playlists: ☑ Enabled                      │
│  │ Cross-service validation: ☑ Enabled                 │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ➕ Connect Additional Service                        │
│  │ [Add Apple Music] [Add Spotify] [Add Other]         │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 3. Component Specifications

### Standardized Progress Component

#### ProgressIndicator Component (Horizontal Bar Only)
```typescript
interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  stepLabels: string[]; // Required - always show labels
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  stepLabels,
}) => {
  return (
    <div className="w-full max-w-4xl mx-auto mb-8">
      {/* Step Labels */}
      <div className="flex justify-between mb-2">
        {stepLabels.map((label, index) => (
          <div
            key={index}
            className={`text-sm font-medium ${
              index + 1 <= currentStep
                ? 'text-purple-600'
                : index + 1 === currentStep + 1
                ? 'text-purple-400'
                : 'text-gray-400'
            }`}
          >
            {label}
          </div>
        ))}
      </div>

      {/* Progress Bar */}
      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
        <div
          className="h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-300"
          style={{ width: `${(currentStep / totalSteps) * 100}%` }}
        />
      </div>

      {/* Step Counter */}
      <div className="text-center mt-2">
        <span className="text-sm text-gray-600">
          Step {currentStep} of {totalSteps}
        </span>
      </div>
    </div>
  );
};

// Usage in onboarding
const stepLabels = ['Welcome', 'Profile', 'Music Service', 'Session Setup'];
<ProgressIndicator
  currentStep={currentStep}
  totalSteps={4}
  stepLabels={stepLabels}
/>
```

### Form Validation Components

#### ValidatedInput Component
```typescript
interface ValidatedInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: string) => string | null;
  };
  placeholder?: string;
  type?: 'text' | 'email' | 'tel';
  disabled?: boolean;
  autoComplete?: string;
}

const ValidatedInput: React.FC<ValidatedInputProps> = ({
  label,
  value,
  onChange,
  validation = {},
  placeholder,
  type = 'text',
  disabled = false,
  autoComplete,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [touched, setTouched] = useState(false);

  const validateValue = useCallback((val: string) => {
    if (validation.required && !val.trim()) {
      return `${label} is required`;
    }

    if (validation.minLength && val.length < validation.minLength) {
      return `${label} must be at least ${validation.minLength} characters`;
    }

    if (validation.maxLength && val.length > validation.maxLength) {
      return `${label} cannot exceed ${validation.maxLength} characters`;
    }

    if (validation.pattern && !validation.pattern.test(val)) {
      return `${label} format is invalid`;
    }

    if (validation.custom) {
      return validation.custom(val);
    }

    return null;
  }, [label, validation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    if (touched) {
      setError(validateValue(newValue));
    }
  };

  const handleBlur = () => {
    setTouched(true);
    setError(validateValue(value));
  };

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {validation.required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <input
        type={type}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        autoComplete={autoComplete}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors ${
          error
            ? 'border-red-500 bg-red-50'
            : 'border-gray-300 bg-white'
        } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        aria-invalid={!!error}
        aria-describedby={error ? `${label}-error` : undefined}
      />

      {error && (
        <p
          id={`${label}-error`}
          className="text-sm text-red-600"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  );
};
```

### Music Service Connection Components

#### Simplified ServiceConnectionCard Component
```typescript
interface SimplifiedServiceCardProps {
  service: 'apple' | 'spotify';
  connecting: boolean;
  onConnect: () => void;
  onLearnMore: () => void;
}

const SimplifiedServiceCard: React.FC<SimplifiedServiceCardProps> = ({
  service,
  connecting,
  onConnect,
  onLearnMore,
}) => {
  const serviceConfig = {
    apple: {
      name: 'Apple Music',
      icon: '🍎',
      color: 'from-gray-900 to-gray-700',
      shortDescription: 'Full playlist integration',
      requirement: 'Requires Apple Music subscription',
    },
    spotify: {
      name: 'Spotify',
      icon: '🎵',
      color: 'from-green-600 to-green-400',
      shortDescription: 'Song search & validation',
      requirement: 'Works with free accounts',
    },
  };

  const config = serviceConfig[service];

  return (
    <div className="p-6 rounded-xl border-2 border-gray-200 bg-white hover:border-gray-300 transition-all duration-200">
      <div className="flex items-center space-x-4 mb-4">
        <div className={`w-16 h-16 rounded-lg bg-gradient-to-br ${config.color} flex items-center justify-center text-3xl`}>
          {config.icon}
        </div>
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 mb-1">
            {config.name}
          </h3>
          <p className="text-sm text-gray-600 mb-2">
            {config.shortDescription}
          </p>
          <p className="text-xs text-gray-500">
            {config.requirement}
          </p>
        </div>
      </div>

      <div className="flex space-x-3">
        <Button
          onClick={onConnect}
          disabled={connecting}
          className="flex-1"
          size="lg"
        >
          {connecting ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Connecting...
            </>
          ) : (
            `Connect ${config.name}`
          )}
        </Button>
      </div>
    </div>
  );
};

// Skip option card
const SkipServiceCard: React.FC<{ onSkip: () => void }> = ({ onSkip }) => (
  <div className="p-6 rounded-xl border-2 border-gray-200 bg-gray-50 transition-all duration-200">
    <div className="flex items-center space-x-4 mb-4">
      <div className="w-16 h-16 rounded-lg bg-gray-300 flex items-center justify-center text-3xl">
        ⏭️
      </div>
      <div className="flex-1">
        <h3 className="text-xl font-semibold text-gray-900 mb-1">
          Skip for Now
        </h3>
        <p className="text-sm text-gray-600 mb-2">
          Manual song entry only
        </p>
        <p className="text-xs text-gray-500">
          Can connect a service later
        </p>
      </div>
    </div>

    <Button
      onClick={onSkip}
      variant="outline"
      className="w-full"
      size="lg"
    >
      Continue with Manual Entry
    </Button>
  </div>
);
```

## 4. Session Creation with Single Service

### Simplified Session Creation

Since DJs can only connect one music service, session creation is simplified to use the connected service automatically.

#### Session Creation Interface (No Service Selection)

```typescript
interface SessionCreationProps {
  connectedService: 'apple' | 'spotify' | 'manual';
  onSessionCreate: (sessionData: SessionFormData) => void;
}

const SessionCreation: React.FC<SessionCreationProps> = ({
  connectedService,
  onSessionCreate,
}) => {
  const serviceDisplay = {
    apple: { name: 'Apple Music', icon: '🍎', description: 'Full playlist integration' },
    spotify: { name: 'Spotify', icon: '🎵', description: 'Song search & validation' },
    manual: { name: 'Manual Entry', icon: '✏️', description: 'Basic functionality' },
  };

  const currentService = serviceDisplay[connectedService];

  return (
    <div className="space-y-6">
      {/* Session Form */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">
          Create Your Session
        </h3>

        {/* Session fields would go here */}

        {/* Music Service Display (Read-only) */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{currentService.icon}</div>
            <div>
              <h4 className="font-medium text-gray-900">
                Music Service: {currentService.name}
              </h4>
              <p className="text-sm text-gray-600">
                {currentService.description}
              </p>
            </div>
            {connectedService !== 'manual' && (
              <div className="ml-auto">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Connected
                </span>
              </div>
            )}
          </div>

          {connectedService !== 'manual' && (
            <div className="mt-3 text-sm text-gray-600">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {/* Open service settings */}}
              >
                Change Music Service in Settings
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
```

### Impact of Service Selection on Session Functionality

#### Apple Music Selected
- **Song Search**: Full MusicKit API access with comprehensive catalog
- **Request Validation**: Automatic validation against Apple Music database
- **Playlist Integration**: Approved songs automatically added to Apple Music playlist
- **Preview Playback**: 30-second previews available for DJs
- **Metadata**: High-quality album artwork, genre tags, release information
- **Duplicate Detection**: Smart detection based on Apple Music IDs

#### Spotify Selected
- **Song Search**: Spotify Web API with extensive catalog
- **Request Validation**: Validation against Spotify database with confidence scoring
- **Playlist Integration**: Manual playlist management (no automatic adding)
- **Preview Playback**: 30-second previews where available
- **Metadata**: Album artwork, genre classification, popularity metrics
- **Duplicate Detection**: Based on Spotify track IDs and metadata matching

#### Manual Entry Selected
- **Song Search**: No automatic search (manual entry only)
- **Request Validation**: Basic text validation only
- **Playlist Integration**: None (manual tracking only)
- **Preview Playback**: Not available
- **Metadata**: User-provided information only
- **Duplicate Detection**: Basic text matching

## 5. Implementation Guidelines

### Database Schema Updates

#### DJ Profile Extensions (Simplified Single Service)
```typescript
// Convex schema updates for djProfiles table (simplified)
export const djProfiles = defineTable({
  userId: v.id("users"),
  displayName: v.optional(v.string()),
  bio: v.optional(v.string()),
  venueType: v.optional(v.string()),
  yearsExperience: v.optional(v.string()),
  location: v.optional(v.string()),
  profilePictureStorageId: v.optional(v.id("_storage")),

  // Single music service connection (simplified)
  connectedMusicService: v.optional(v.union(v.literal("apple"), v.literal("spotify"), v.literal("manual"))),
  musicServiceData: v.optional(v.object({
    // Apple Music fields (only if connectedMusicService === "apple")
    appleMusicToken: v.optional(v.string()),
    appleMusicLastSync: v.optional(v.number()),
    // Spotify fields (only if connectedMusicService === "spotify")
    spotifyAccessToken: v.optional(v.string()),
    spotifyRefreshToken: v.optional(v.string()),
    spotifyLastSync: v.optional(v.number()),
  })),

  // Session defaults (simplified)
  defaultSessionDuration: v.optional(v.number()),
  defaultRequestLimit: v.optional(v.number()),
  defaultRequestHandling: v.optional(v.union(v.literal("ask"), v.literal("auto-accept"), v.literal("auto-deny"))),
  defaultGenreBlocks: v.optional(v.array(v.string())),

  // Simplified notification preferences
  defaultNotifications: v.optional(v.union(v.literal("immediate"), v.literal("batched"), v.literal("off"))),

  // Onboarding tracking
  completedOnboarding: v.boolean(),
  onboardingStep: v.optional(v.number()),
  onboardingData: v.optional(v.any()), // Temporary storage for incomplete onboarding

  createdAt: v.number(),
  updatedAt: v.number(),
  lastLoginAt: v.optional(v.number()),
})
.index("by_user", ["userId"])
.index("by_onboarding_status", ["completedOnboarding"])
.index("by_music_service", ["connectedMusicService"]);
```

#### Session Schema Updates (Simplified)
```typescript
// Simplified session schema - uses DJ's connected service
export const sessions = defineTable({
  // ... existing fields
  // Music service is inherited from DJ profile - no per-session selection
  musicServiceOverride: v.optional(v.union(v.literal("manual"))), // Only allow manual override
  musicServiceConfig: v.optional(v.object({
    playlistId: v.optional(v.string()), // Single playlist ID field
    autoAddSongs: v.optional(v.boolean()),
  })),
  // ... rest of schema
});
```

### API Endpoints

#### Onboarding Progress Management
```typescript
// convex/onboarding.ts
export const saveOnboardingProgress = mutation({
  args: {
    step: v.number(),
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    await ctx.db.patch(djProfile._id, {
      onboardingStep: args.step,
      onboardingData: args.data,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const getOnboardingProgress = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return djProfile ? {
      step: djProfile.onboardingStep || 1,
      data: djProfile.onboardingData || {},
      completed: djProfile.completedOnboarding,
    } : null;
  },
});
```

#### Music Service Management (Single Service)
```typescript
// convex/musicServices.ts (simplified for single service)
export const connectAppleMusic = mutation({
  args: {
    userToken: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    // Disconnect any existing service first (single service enforcement)
    await ctx.db.patch(djProfile._id, {
      connectedMusicService: "apple",
      musicServiceData: {
        appleMusicToken: args.userToken,
        appleMusicLastSync: Date.now(),
      },
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const connectSpotify = mutation({
  args: {
    accessToken: v.string(),
    refreshToken: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    // Disconnect any existing service first (single service enforcement)
    await ctx.db.patch(djProfile._id, {
      connectedMusicService: "spotify",
      musicServiceData: {
        spotifyAccessToken: args.accessToken,
        spotifyRefreshToken: args.refreshToken,
        spotifyLastSync: Date.now(),
      },
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const disconnectMusicService = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    await ctx.db.patch(djProfile._id, {
      connectedMusicService: "manual",
      musicServiceData: undefined,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});
```

### Component Integration Points

#### Onboarding Router Component
```typescript
// components/onboarding/OnboardingRouter.tsx
const OnboardingRouter: React.FC = () => {
  const { isAuthenticated } = useConvexAuth();
  const userWithProfile = useQuery(api.users.getCurrentUserWithProfile);
  const onboardingProgress = useQuery(api.onboarding.getOnboardingProgress);

  if (!isAuthenticated || !userWithProfile) {
    return <LoadingSpinner />;
  }

  // If onboarding is complete, redirect to dashboard
  if (userWithProfile.djProfile?.completedOnboarding) {
    return <Navigate to="/dashboard" replace />;
  }

  const currentStep = onboardingProgress?.step || 1;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        <ProgressIndicator
          currentStep={currentStep}
          totalSteps={4}
          stepLabels={[
            'Welcome',
            'Profile',
            'Music Service',
            'Session Setup'
          ]}
        />

        <div className="mt-8">
          {currentStep === 1 && <WelcomeScreen />}
          {currentStep === 2 && <ProfileSetup />}
          {currentStep === 3 && <MusicServiceSetup />}
          {currentStep === 4 && <SessionDefaultsAndCreation />}
        </div>
      </div>
    </div>
  );
};
```

### Mobile Responsiveness Guidelines

#### Breakpoint Strategy
```css
/* Tailwind CSS breakpoints for onboarding */
.onboarding-container {
  @apply px-4 py-6;

  @screen sm {
    @apply px-6 py-8;
  }

  @screen md {
    @apply px-8 py-12;
  }

  @screen lg {
    @apply px-12 py-16;
  }
}

.onboarding-card {
  @apply w-full max-w-md mx-auto;

  @screen md {
    @apply max-w-2xl;
  }

  @screen lg {
    @apply max-w-4xl;
  }
}

.onboarding-form-grid {
  @apply grid grid-cols-1 gap-4;

  @screen md {
    @apply grid-cols-2 gap-6;
  }

  @screen lg {
    @apply gap-8;
  }
}
```

#### Touch Target Guidelines
- Minimum touch target size: 44px x 44px
- Spacing between interactive elements: 8px minimum
- Button padding: 12px vertical, 16px horizontal on mobile
- Form inputs: 48px height on mobile, 40px on desktop

### Accessibility Requirements

#### ARIA Labels and Roles
```typescript
// Example accessible form component
<form role="form" aria-labelledby="profile-setup-title">
  <h2 id="profile-setup-title">DJ Profile Setup</h2>

  <div className="form-group">
    <label htmlFor="dj-name" className="required">
      DJ Display Name
    </label>
    <input
      id="dj-name"
      type="text"
      required
      aria-required="true"
      aria-describedby="dj-name-help dj-name-error"
      aria-invalid={hasError}
    />
    <div id="dj-name-help" className="help-text">
      This name will be visible to your audience
    </div>
    {hasError && (
      <div id="dj-name-error" role="alert" className="error-text">
        Display name is required
      </div>
    )}
  </div>
</form>
```

#### Keyboard Navigation
- Tab order follows logical flow
- All interactive elements focusable
- Skip links for screen readers
- Escape key closes modals/dropdowns
- Enter key submits forms
- Arrow keys navigate radio groups

### Performance Optimization

#### Code Splitting
```typescript
// Lazy load onboarding components
const WelcomeScreen = lazy(() => import('./components/WelcomeScreen'));
const ProfileSetup = lazy(() => import('./components/ProfileSetup'));
const MusicServiceSetup = lazy(() => import('./components/MusicServiceSetup'));

// Preload next step component (updated for 4 steps)
const preloadNextStep = (currentStep: number) => {
  const nextComponents = {
    1: () => import('./components/ProfileSetup'),
    2: () => import('./components/MusicServiceSetup'),
    3: () => import('./components/SessionDefaultsAndCreation'),
  };

  const preloader = nextComponents[currentStep as keyof typeof nextComponents];
  if (preloader) {
    preloader();
  }
};
```

#### Image Optimization
- Profile pictures: WebP format with JPEG fallback
- Maximum upload size: 5MB
- Automatic resizing to 400x400px
- Progressive loading with blur placeholder
- Lazy loading for non-critical images

This completes the comprehensive DJ onboarding design with detailed implementation guidelines, component specifications, and technical requirements.
