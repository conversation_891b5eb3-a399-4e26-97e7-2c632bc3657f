# PlayBeg DJ Onboarding Journey Design

## Executive Summary

This document outlines a comprehensive DJ onboarding experience that guides new users from account signup through their first session creation. The design emphasizes progressive disclosure, flexible music service integration, and mobile-responsive interfaces while building on the existing Convex-based architecture.

## Current State Analysis

### Existing Implementation
- ✅ Basic DJ profile creation via `djProfiles.ts`
- ✅ Onboarding completion tracking (`completedOnboarding` field)
- ✅ Apple Music integration with MusicKit
- ✅ Spotify integration for song search and validation
- ✅ Session creation workflow
- ✅ Mobile-responsive design patterns

### Gaps Identified
- ❌ No structured onboarding flow after signup
- ❌ No music service selection interface
- ❌ No guided first session creation
- ❌ No progressive feature introduction
- ❌ Limited multi-service support

## 1. Post-Signup Onboarding Flow

### Flow Overview
```
Account Signup → Welcome Screen → Profile Setup → Music Service Selection → 
Preferences Configuration → First Session Creation → Onboarding Complete
```

### Step 1: Welcome & Orientation Screen

**Purpose**: Orient new DJs and set expectations
**Duration**: 30-45 seconds
**Skip Option**: No (critical orientation)

#### Desktop Layout (1200px+)
```
┌─────────────────────────────────────────────────────────────┐
│ [PlayBeg Logo]                                    [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│           🎧 Welcome to PlayBeg, [DJ Name]!                │
│                                                             │
│    Transform how you connect with your audience            │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │     📱      │  │     🎵      │  │     📊      │        │
│  │ QR Requests │  │ Real-time   │  │ Analytics   │        │
│  │             │  │ Management  │  │ & Insights  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│              [Let's Get Started] [Watch Demo]              │
│                                                             │
│                    ● ○ ○ ○ ○                               │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout (320px-768px)
```
┌─────────────────────────────┐
│ [PlayBeg Logo]        [Skip]│
├─────────────────────────────┤
│                             │
│    🎧 Welcome to PlayBeg!   │
│                             │
│  Transform how you connect  │
│     with your audience      │
│                             │
│        ┌─────────────┐      │
│        │     📱      │      │
│        │ QR Requests │      │
│        └─────────────┘      │
│                             │
│        ┌─────────────┐      │
│        │     🎵      │      │
│        │ Real-time   │      │
│        │ Management  │      │
│        └─────────────┘      │
│                             │
│    [Let's Get Started]      │
│       [Watch Demo]          │
│                             │
│         ● ○ ○ ○ ○           │
└─────────────────────────────┘
```

#### Component Specifications
- **Header**: Fixed position with skip option (saves progress)
- **Hero Section**: Animated DJ name insertion with typewriter effect
- **Feature Cards**: Hover animations on desktop, tap animations on mobile
- **CTA Buttons**: Primary action (Let's Get Started), Secondary (Watch Demo)
- **Progress Indicator**: 5-dot indicator showing current step
- **Animations**: Fade-in sequence for feature cards (300ms stagger)

#### Validation & Error Handling
- No validation required (informational screen)
- Auto-advance after 60 seconds if no interaction
- Skip button saves current progress state

### Step 2: DJ Profile Setup

**Purpose**: Collect essential DJ information and preferences
**Duration**: 2-3 minutes
**Skip Option**: Partial (can complete later)

#### Form Fields & Validation

**Required Fields:**
1. **DJ Display Name**
   - Input type: Text
   - Validation: 2-50 characters, no special characters except spaces, hyphens, underscores
   - Error messages: "Display name must be 2-50 characters" / "Only letters, numbers, spaces, hyphens, and underscores allowed"
   - Real-time validation with debounced checking

2. **Primary Venue Type** (Single select)
   - Options: Nightclub, Wedding Venue, Corporate Events, Private Parties, Mobile DJ, Other
   - Default: None selected
   - Validation: Required selection
   - Error message: "Please select your primary venue type"

**Optional Fields:**
3. **Profile Picture**
   - Input type: File upload (drag & drop + click to browse)
   - Accepted formats: JPG, PNG, WebP
   - Max size: 5MB
   - Auto-resize to 400x400px
   - Validation: File type and size checking
   - Error messages: "Please upload a JPG, PNG, or WebP image" / "Image must be under 5MB"

4. **Bio/Description**
   - Input type: Textarea
   - Character limit: 500 characters
   - Placeholder: "Tell your audience about your style and experience..."
   - Validation: Max 500 characters
   - Character counter with color coding (green < 400, yellow 400-480, red > 480)

5. **Years of Experience**
   - Input type: Select dropdown
   - Options: New to DJing, 1-2 years, 3-5 years, 6-10 years, 10+ years
   - Default: None selected
   - Optional field

6. **Location** (City, State/Country)
   - Input type: Text with autocomplete
   - Validation: Optional, 2-100 characters if provided
   - Placeholder: "e.g., Los Angeles, CA"

#### Desktop Layout
```
┌─────────────────────────────────────────────────────────────┐
│ [← Back]              DJ Profile Setup              [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐                                           │
│  │             │  DJ Display Name *                        │
│  │   Upload    │  ┌─────────────────────────────────────┐  │
│  │   Photo     │  │ [Input Field]                       │  │
│  │             │  └─────────────────────────────────────┘  │
│  │  [Browse]   │                                           │
│  └─────────────┘  Primary Venue Type *                     │
│                   ┌─────────────────────────────────────┐  │
│                   │ [Dropdown: Select venue type...]   │  │
│                   └─────────────────────────────────────┘  │
│                                                             │
│  Bio/Description (Optional)                                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │ [Textarea - 500 char limit]                        │  │
│  │                                                     │  │
│  └─────────────────────────────────────────────────────┘  │
│  Characters: 0/500                                         │
│                                                             │
│  Years of Experience        Location                       │
│  ┌─────────────────────┐    ┌─────────────────────────┐    │
│  │ [Select...]         │    │ [City, State/Country]   │    │
│  └─────────────────────┘    └─────────────────────────┘    │
│                                                             │
│                    [Save & Continue]                       │
│                                                             │
│                       ● ● ○ ○ ○                           │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout
```
┌─────────────────────────────┐
│ [← Back] Profile Setup [Skip]│
├─────────────────────────────┤
│                             │
│      ┌─────────────┐        │
│      │             │        │
│      │   Upload    │        │
│      │   Photo     │        │
│      │             │        │
│      │  [Browse]   │        │
│      └─────────────┘        │
│                             │
│ DJ Display Name *           │
│ ┌─────────────────────────┐ │
│ │ [Input Field]           │ │
│ └─────────────────────────┘ │
│                             │
│ Primary Venue Type *        │
│ ┌─────────────────────────┐ │
│ │ [Select venue type...]  │ │
│ └─────────────────────────┘ │
│                             │
│ Bio (Optional)              │
│ ┌─────────────────────────┐ │
│ │ [Textarea]              │ │
│ │                         │ │
│ └─────────────────────────┘ │
│ Characters: 0/500           │
│                             │
│ Experience                  │
│ ┌─────────────────────────┐ │
│ │ [Select...]             │ │
│ └─────────────────────────┘ │
│                             │
│ Location                    │
│ ┌─────────────────────────┐ │
│ │ [City, State/Country]   │ │
│ └─────────────────────────┘ │
│                             │
│    [Save & Continue]        │
│                             │
│        ● ● ○ ○ ○            │
└─────────────────────────────┘
```

#### Component Specifications
- **File Upload**: Drag & drop zone with preview, progress indicator, error states
- **Form Validation**: Real-time validation with inline error messages
- **Character Counter**: Dynamic color coding and real-time updates
- **Save State**: Auto-save draft every 30 seconds
- **Accessibility**: Full keyboard navigation, screen reader support, ARIA labels

#### API Integration
```typescript
// Convex mutation call
const updateProfile = useMutation(api.djProfiles.updateDjProfile);

const handleSave = async (formData: ProfileFormData) => {
  try {
    await updateProfile({
      profileId: djProfile._id,
      displayName: formData.displayName,
      // ... other fields
    });
    
    // Proceed to next step
    setCurrentStep(3);
  } catch (error) {
    // Handle validation errors
    setErrors(error.validationErrors);
  }
};
```

### Step 3: Music Service Integration

**Purpose**: Configure music streaming service for song search and playlist management
**Duration**: 1-2 minutes
**Skip Option**: Yes (can configure later, but limits functionality)

#### Service Selection Interface

**Available Options:**
1. **Apple Music** (Recommended for iOS users)
   - Features: Full playlist integration, MusicKit API
   - Requirements: Apple Music subscription
   - Capabilities: Song search, playlist creation, automatic adding

2. **Spotify** (Recommended for broader compatibility)
   - Features: Song search and validation
   - Requirements: Spotify account (free or premium)
   - Capabilities: Song search, metadata validation, limited playlist features

3. **Both Services** (Advanced users)
   - Features: Maximum compatibility and flexibility
   - Requirements: Both service accounts
   - Capabilities: Per-session service selection

4. **Skip for Now**
   - Features: Basic functionality only
   - Limitations: Manual song entry, no automatic validation
   - Note: Can be configured later in settings

#### Desktop Layout
```
┌─────────────────────────────────────────────────────────────┐
│ [← Back]           Music Service Setup              [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│           Choose your preferred music service               │
│                                                             │
│  ┌─────────────────────┐    ┌─────────────────────┐        │
│  │    🍎 Apple Music   │    │   🎵 Spotify        │        │
│  │                     │    │                     │        │
│  │ ✓ Full integration  │    │ ✓ Song search       │        │
│  │ ✓ Playlist creation │    │ ✓ Metadata validation│       │
│  │ ✓ Auto-add songs   │    │ ✓ Broad compatibility│       │
│  │                     │    │                     │        │
│  │ Requires: Apple     │    │ Requires: Spotify   │        │
│  │ Music subscription  │    │ account (free/paid) │        │
│  │                     │    │                     │        │
│  │   [Connect Apple]   │    │  [Connect Spotify]  │        │
│  └─────────────────────┘    └─────────────────────┘        │
│                                                             │
│  ┌─────────────────────┐    ┌─────────────────────┐        │
│  │   🎼 Both Services  │    │   ⏭️ Skip for Now   │        │
│  │                     │    │                     │        │
│  │ ✓ Maximum flexibility│   │ ⚠️ Limited features │        │
│  │ ✓ Per-session choice│    │ ⚠️ Manual song entry│        │
│  │ ✓ Best compatibility│    │ ⚠️ No validation    │        │
│  │                     │    │                     │        │
│  │ Requires: Both      │    │ Can configure later │        │
│  │ service accounts    │    │ in settings         │        │
│  │                     │    │                     │        │
│  │  [Setup Both]       │    │  [Continue Basic]   │        │
│  └─────────────────────┘    └─────────────────────┘        │
│                                                             │
│                       ● ● ● ○ ○                           │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout (Stacked Cards)
```
┌─────────────────────────────┐
│ [← Back] Music Setup  [Skip]│
├─────────────────────────────┤
│                             │
│   Choose your music service │
│                             │
│ ┌─────────────────────────┐ │
│ │    🍎 Apple Music       │ │
│ │                         │ │
│ │ ✓ Full integration      │ │
│ │ ✓ Playlist creation     │ │
│ │ ✓ Auto-add songs       │ │
│ │                         │ │
│ │ Requires: Apple Music   │ │
│ │ subscription            │ │
│ │                         │ │
│ │   [Connect Apple]       │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │   🎵 Spotify            │ │
│ │                         │ │
│ │ ✓ Song search           │ │
│ │ ✓ Metadata validation   │ │
│ │ ✓ Broad compatibility   │ │
│ │                         │ │
│ │ Requires: Spotify       │ │
│ │ account (free/paid)     │ │
│ │                         │ │
│ │  [Connect Spotify]      │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │   🎼 Both Services      │ │
│ │                         │ │
│ │ ✓ Maximum flexibility   │ │
│ │ ✓ Per-session choice    │ │
│ │                         │ │
│ │  [Setup Both]           │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │   ⏭️ Skip for Now       │ │
│ │                         │ │
│ │ ⚠️ Limited features     │ │
│ │ Can configure later     │ │
│ │                         │ │
│ │  [Continue Basic]       │ │
│ └─────────────────────────┘ │
│                             │
│        ● ● ● ○ ○            │
└─────────────────────────────┘
```

#### Service Connection Flows

**Apple Music Connection:**
1. Click "Connect Apple" → MusicKit authorization popup
2. User grants permission → Token stored securely
3. Connection verified → Success state with checkmark
4. Auto-advance to next step

**Spotify Connection:**
1. Click "Connect Spotify" → OAuth redirect to Spotify
2. User authorizes PlayBeg → Redirect back with auth code
3. Token exchange and storage → Success confirmation
4. Auto-advance to next step

**Both Services Setup:**
1. Sequential connection flow (Apple Music first, then Spotify)
2. Progress indicator showing current service
3. Success state shows both services connected
4. Advanced options for default service selection

#### Component Specifications
- **Service Cards**: Hover/tap animations, clear feature comparison
- **Connection States**: Loading, success, error with retry options
- **Progress Tracking**: Visual indicators for multi-step connections
- **Error Handling**: Clear error messages with troubleshooting links
- **Security**: OAuth flows with secure token storage

### Step 4: Initial Preferences Configuration

**Purpose**: Set default preferences for session creation and management
**Duration**: 1-2 minutes
**Skip Option**: Yes (uses sensible defaults)

#### Desktop Layout
```
┌─────────────────────────────────────────────────────────────┐
│ [← Back]           Set Your Preferences            [Skip] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Session Defaults                 Notifications             │
│  ┌─────────────────────────┐     ┌─────────────────────────┐ │
│  │ Default Duration        │     │ New Request Alerts      │ │
│  │ ○ 2 hours  ● 4 hours   │     │ ● Immediate             │ │
│  │ ○ 6 hours  ○ 8 hours   │     │ ○ Batched (every 5 min)│ │
│  │ ○ Custom: [___] hours  │     │ ○ Off                   │ │
│  │                         │     │                         │ │
│  │ Request Handling        │     │ Milestone Alerts        │ │
│  │ ● Ask each time         │     │ ☑ 10 requests          │ │
│  │ ○ Auto-accept all      │     │ ☑ 25 requests          │ │
│  │ ○ Auto-deny explicit   │     │ ☑ 50 requests          │ │
│  │                         │     │ ☑ 100 requests         │ │
│  │ Requests per Person     │     │                         │ │
│  │ ○ 1  ● 3  ○ 5  ○ 10   │     │ Email Summaries         │ │
│  │ ○ Unlimited            │     │ ● After each session    │ │
│  └─────────────────────────┘     │ ○ Weekly  ○ Never      │ │
│                                   └─────────────────────────┘ │
│                                                             │
│  Genre Blocking (Optional)                                  │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ☐ Explicit Content  ☐ Heavy Metal  ☐ Country        │   │
│  │ ☐ Rap/Hip-Hop      ☐ Classical    ☐ Death Metal     │   │
│  │ ☐ Reggaeton        ☐ Opera        ☐ Custom: [____]  │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│                    [Save Preferences]                      │
│                                                             │
│                       ● ● ● ● ○                           │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout
```
┌─────────────────────────────┐
│ [← Back] Preferences  [Skip]│
├─────────────────────────────┤
│                             │
│ Session Defaults            │
│ ┌─────────────────────────┐ │
│ │ Default Duration        │ │
│ │ ○ 2h  ● 4h  ○ 6h  ○ 8h │ │
│ │ ○ Custom: [___] hours   │ │
│ │                         │ │
│ │ Request Handling        │ │
│ │ ● Ask each time         │ │
│ │ ○ Auto-accept all       │ │
│ │ ○ Auto-deny explicit    │ │
│ │                         │ │
│ │ Requests per Person     │ │
│ │ ○ 1  ● 3  ○ 5  ○ 10    │ │
│ │ ○ Unlimited             │ │
│ └─────────────────────────┘ │
│                             │
│ Notifications               │
│ ┌─────────────────────────┐ │
│ │ New Request Alerts      │ │
│ │ ● Immediate             │ │
│ │ ○ Batched (every 5 min) │ │
│ │ ○ Off                   │ │
│ │                         │ │
│ │ Milestone Alerts        │ │
│ │ ☑ 10  ☑ 25  ☑ 50  ☑ 100│ │
│ │                         │ │
│ │ Email Summaries         │ │
│ │ ● After each session    │ │
│ │ ○ Weekly  ○ Never       │ │
│ └─────────────────────────┘ │
│                             │
│ Genre Blocking (Optional)   │
│ ┌─────────────────────────┐ │
│ │ ☐ Explicit Content      │ │
│ │ ☐ Heavy Metal           │ │
│ │ ☐ Country               │ │
│ │ ☐ Rap/Hip-Hop           │ │
│ │ ☐ Classical             │ │
│ │ ☐ Custom: [_________]   │ │
│ └─────────────────────────┘ │
│                             │
│    [Save Preferences]       │
│                             │
│        ● ● ● ● ○            │
└─────────────────────────────┘
```

### Step 5: First Session Creation (Guided)

**Purpose**: Walk through creating their first session with contextual help
**Duration**: 2-3 minutes
**Skip Option**: No (essential for completing onboarding)

#### Guided Session Creation Interface

**Session Information:**
- Session name (required): "My First PlayBeg Session" (pre-filled, editable)
- Event type: Dropdown with onboarding-specific options
- Venue/location (optional): Text input with autocomplete
- Expected duration: Uses preference from Step 4 as default

**Music Service Selection** (if multiple configured):
- Radio buttons for Apple Music/Spotify/Both
- Contextual help explaining differences
- Preview of how choice affects functionality

**Advanced Settings** (Collapsed by default):
- Genre blocking (uses preferences as default)
- Request limits (uses preferences as default)
- Auto-approval settings
- Wedding mode toggle

#### Desktop Layout
```
┌─────────────────────────────────────────────────────────────┐
│ [← Back]         Create Your First Session                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Let's create your first session! 🎉                       │
│                                                             │
│  Session Information                                        │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Session Name *                                      │   │
│  │ ┌─────────────────────────────────────────────────┐ │   │
│  │ │ My First PlayBeg Session                        │ │   │
│  │ └─────────────────────────────────────────────────┘ │   │
│  │                                                     │   │
│  │ Event Type *                                        │   │
│  │ ┌─────────────────────────────────────────────────┐ │   │
│  │ │ Practice Session ▼                              │ │   │
│  │ └─────────────────────────────────────────────────┘ │   │
│  │                                                     │   │
│  │ Venue/Location (Optional)                           │   │
│  │ ┌─────────────────────────────────────────────────┐ │   │
│  │ │ e.g., Home Studio, Local Venue...              │ │   │
│  │ └─────────────────────────────────────────────────┘ │   │
│  │                                                     │   │
│  │ Duration: ● 4 hours (from your preferences)        │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Music Service (You connected: Apple Music ✓)              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ● Apple Music - Full playlist integration          │   │
│  │ ○ Manual Entry - Basic functionality only          │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ▼ Advanced Settings (Optional)                            │
│                                                             │
│                    [Create Session]                        │
│                                                             │
│                       ● ● ● ● ●                           │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile Layout
```
┌─────────────────────────────┐
│ [← Back] First Session      │
├─────────────────────────────┤
│                             │
│ Let's create your first     │
│ session! 🎉                 │
│                             │
│ Session Information         │
│ ┌─────────────────────────┐ │
│ │ Session Name *          │ │
│ │ ┌─────────────────────┐ │ │
│ │ │ My First PlayBeg    │ │ │
│ │ │ Session             │ │ │
│ │ └─────────────────────┘ │ │
│ │                         │ │
│ │ Event Type *            │ │
│ │ ┌─────────────────────┐ │ │
│ │ │ Practice Session ▼  │ │ │
│ │ └─────────────────────┘ │ │
│ │                         │ │
│ │ Venue (Optional)        │ │
│ │ ┌─────────────────────┐ │ │
│ │ │ Home Studio...      │ │ │
│ │ └─────────────────────┘ │ │
│ │                         │ │
│ │ Duration: 4 hours       │ │
│ └─────────────────────────┘ │
│                             │
│ Music Service               │
│ ┌─────────────────────────┐ │
│ │ ● Apple Music ✓         │ │
│ │   Full integration      │ │
│ │                         │ │
│ │ ○ Manual Entry          │ │
│ │   Basic functionality   │ │
│ └─────────────────────────┘ │
│                             │
│ ▼ Advanced Settings         │
│                             │
│    [Create Session]         │
│                             │
│        ● ● ● ● ●            │
└─────────────────────────────┘
```

### Step 6: Onboarding Complete & Next Steps

**Purpose**: Celebrate completion and guide toward first actions
**Duration**: 30 seconds
**Skip Option**: No (completion screen)

#### Success Screen Layout

**Desktop:**
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                        🎉 Congratulations!                 │
│                                                             │
│              Your DJ profile is ready to rock!             │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                Session Created ✓                    │   │
│  │                                                     │   │
│  │        "My First PlayBeg Session"                   │   │
│  │                                                     │   │
│  │    🔗 playbeg.com/session/abc123                   │   │
│  │                                                     │   │
│  │              [Copy Link] [Show QR]                 │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│                      What's Next?                          │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │     📱      │  │     🎵      │  │     📚      │        │
│  │ Share Your  │  │ Test Song   │  │ Learn More  │        │
│  │ QR Code     │  │ Requests    │  │ Features    │        │
│  │             │  │             │  │             │        │
│  │ [Share]     │  │ [Test]      │  │ [Guide]     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│                  [Go to Dashboard]                         │
│                                                             │
│                       ● ● ● ● ●                           │
└─────────────────────────────────────────────────────────────┘
```

**Mobile:**
```
┌─────────────────────────────┐
│                             │
│      🎉 Congratulations!    │
│                             │
│   Your DJ profile is ready  │
│        to rock!             │
│                             │
│ ┌─────────────────────────┐ │
│ │   Session Created ✓     │ │
│ │                         │ │
│ │ "My First PlayBeg       │ │
│ │  Session"               │ │
│ │                         │ │
│ │ 🔗 playbeg.com/session/ │ │
│ │    abc123               │ │
│ │                         │ │
│ │   [Copy] [Show QR]      │ │
│ └─────────────────────────┘ │
│                             │
│      What's Next?           │
│                             │
│ ┌─────────────────────────┐ │
│ │     📱 Share QR         │ │
│ │     [Share]             │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │     🎵 Test Requests    │ │
│ │     [Test]              │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │     📚 Learn More       │ │
│ │     [Guide]             │ │
│ └─────────────────────────┘ │
│                             │
│    [Go to Dashboard]        │
│                             │
│        ● ● ● ● ●            │
└─────────────────────────────┘
```

#### Component Specifications
- **Success Animation**: Confetti or celebration animation on load
- **Session Summary**: Display created session details with quick actions
- **Next Steps Cards**: Interactive cards with hover/tap animations
- **Quick Actions**: Copy link, show QR code, test functionality
- **Navigation**: Clear path to dashboard with session management

#### API Integration
```typescript
// Mark onboarding as complete
const completeOnboarding = useMutation(api.djProfiles.completeOnboarding);

const handleComplete = async () => {
  try {
    await completeOnboarding({
      displayName: profileData.displayName,
    });

    // Redirect to dashboard
    router.push('/dashboard');
  } catch (error) {
    console.error('Failed to complete onboarding:', error);
  }
};
```

## 2. Music Service Integration Strategy

### Service Architecture Overview

The music service integration supports flexible configuration allowing DJs to:
1. Choose a primary service during onboarding
2. Add additional services later
3. Select different services per session
4. Switch services without losing data

### Service Capabilities Matrix

| Feature | Apple Music | Spotify | Manual Entry |
|---------|-------------|---------|--------------|
| Song Search | ✅ Full | ✅ Full | ❌ Manual |
| Metadata Validation | ✅ Complete | ✅ Complete | ⚠️ Limited |
| Playlist Creation | ✅ Automatic | ⚠️ Limited | ❌ None |
| Auto-Add Songs | ✅ Yes | ❌ No | ❌ No |
| Preview Playback | ✅ 30s | ✅ 30s | ❌ None |
| Album Artwork | ✅ High-res | ✅ High-res | ⚠️ Manual |
| Genre Detection | ✅ Automatic | ✅ Automatic | ⚠️ Manual |
| Duplicate Detection | ✅ Smart | ✅ Smart | ⚠️ Basic |

### Service Selection Interface Design

#### Primary Service Configuration (Onboarding)
```typescript
interface MusicServiceConfig {
  primaryService: 'apple' | 'spotify' | 'manual';
  connectedServices: {
    apple?: {
      connected: boolean;
      userToken?: string;
      lastSync: number;
    };
    spotify?: {
      connected: boolean;
      accessToken?: string;
      refreshToken?: string;
      lastSync: number;
    };
  };
  preferences: {
    defaultService: 'apple' | 'spotify' | 'ask';
    fallbackService: 'apple' | 'spotify' | 'manual';
    autoSync: boolean;
  };
}
```

#### Per-Session Service Selection

When creating a session, DJs can choose which music service to use:

**Desktop Interface:**
```
┌─────────────────────────────────────────────────────────────┐
│ Music Service for this Session                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ● Apple Music (Connected ✓)                               │
│    Full playlist integration, auto-add approved songs      │
│                                                             │
│  ○ Spotify (Connected ✓)                                   │
│    Song search and validation, manual playlist management  │
│                                                             │
│  ○ Manual Entry                                             │
│    Basic functionality, no automatic validation            │
│                                                             │
│  ☑ Remember choice for future sessions                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**Mobile Interface:**
```
┌─────────────────────────────┐
│ Music Service               │
├─────────────────────────────┤
│                             │
│ ● Apple Music ✓             │
│   Full integration          │
│                             │
│ ○ Spotify ✓                 │
│   Search & validation       │
│                             │
│ ○ Manual Entry              │
│   Basic functionality       │
│                             │
│ ☑ Remember choice           │
│                             │
└─────────────────────────────┘
```

### Service Connection Workflows

#### Apple Music Connection Flow
```typescript
// Component: AppleMusicConnector
const connectAppleMusic = async () => {
  setConnectionState('connecting');

  try {
    // Initialize MusicKit if not already done
    await musicService.initialize();

    // Request user authorization
    const success = await musicService.authorize();

    if (success) {
      // Store connection in Convex
      await updateDjProfile({
        profileId: djProfile._id,
        appleMusicConnected: true,
        appleMusicToken: musicService.getUserToken(),
      });

      setConnectionState('connected');
      onConnectionSuccess('apple');
    } else {
      throw new Error('Authorization failed');
    }
  } catch (error) {
    setConnectionState('error');
    setError(error.message);
  }
};
```

#### Spotify Connection Flow
```typescript
// Component: SpotifyConnector
const connectSpotify = async () => {
  setConnectionState('connecting');

  try {
    // Redirect to Spotify OAuth
    const authUrl = `https://accounts.spotify.com/authorize?` +
      `client_id=${SPOTIFY_CLIENT_ID}&` +
      `response_type=code&` +
      `redirect_uri=${REDIRECT_URI}&` +
      `scope=user-read-private user-read-email playlist-modify-public`;

    window.location.href = authUrl;
  } catch (error) {
    setConnectionState('error');
    setError(error.message);
  }
};

// Handle OAuth callback
const handleSpotifyCallback = async (code: string) => {
  try {
    // Exchange code for tokens
    const tokenResponse = await fetch('/api/spotify/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code }),
    });

    const tokens = await tokenResponse.json();

    // Store in Convex
    await updateDjProfile({
      profileId: djProfile._id,
      spotifyConnected: true,
      spotifyTokens: tokens,
    });

    setConnectionState('connected');
    onConnectionSuccess('spotify');
  } catch (error) {
    setConnectionState('error');
    setError(error.message);
  }
};
```

### Service Switching & Management

#### Settings Interface for Service Management
```
┌─────────────────────────────────────────────────────────────┐
│ Music Service Settings                                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Connected Services                                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ 🍎 Apple Music                              ✅ Connected │
│  │ Last synced: 2 hours ago                              │
│  │ [Disconnect] [Test Connection] [Refresh Token]       │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ 🎵 Spotify                                  ✅ Connected │
│  │ Last synced: 5 minutes ago                            │
│  │ [Disconnect] [Test Connection] [Refresh Token]       │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Default Preferences                                        │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Primary Service: ● Apple Music ○ Spotify ○ Ask      │
│  │ Fallback Service: ○ Apple Music ● Spotify ○ Manual  │
│  │ Auto-sync playlists: ☑ Enabled                      │
│  │ Cross-service validation: ☑ Enabled                 │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ➕ Connect Additional Service                        │
│  │ [Add Apple Music] [Add Spotify] [Add Other]         │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 3. Component Specifications

### Onboarding Progress Component

#### ProgressIndicator Component
```typescript
interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  stepLabels?: string[];
  variant?: 'dots' | 'bar' | 'steps';
  showLabels?: boolean;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  stepLabels = [],
  variant = 'dots',
  showLabels = false,
}) => {
  const steps = Array.from({ length: totalSteps }, (_, i) => i + 1);

  return (
    <div className="flex items-center justify-center space-x-2">
      {variant === 'dots' && steps.map((step) => (
        <div
          key={step}
          className={`w-3 h-3 rounded-full transition-colors duration-200 ${
            step <= currentStep
              ? 'bg-purple-500'
              : step === currentStep + 1
              ? 'bg-purple-300'
              : 'bg-gray-300'
          }`}
          aria-label={`Step ${step}${stepLabels[step - 1] ? `: ${stepLabels[step - 1]}` : ''}`}
        />
      ))}

      {variant === 'bar' && (
        <div className="w-64 h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      )}

      {showLabels && stepLabels[currentStep - 1] && (
        <span className="text-sm text-gray-600 ml-4">
          {stepLabels[currentStep - 1]}
        </span>
      )}
    </div>
  );
};
```

### Form Validation Components

#### ValidatedInput Component
```typescript
interface ValidatedInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: string) => string | null;
  };
  placeholder?: string;
  type?: 'text' | 'email' | 'tel';
  disabled?: boolean;
  autoComplete?: string;
}

const ValidatedInput: React.FC<ValidatedInputProps> = ({
  label,
  value,
  onChange,
  validation = {},
  placeholder,
  type = 'text',
  disabled = false,
  autoComplete,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [touched, setTouched] = useState(false);

  const validateValue = useCallback((val: string) => {
    if (validation.required && !val.trim()) {
      return `${label} is required`;
    }

    if (validation.minLength && val.length < validation.minLength) {
      return `${label} must be at least ${validation.minLength} characters`;
    }

    if (validation.maxLength && val.length > validation.maxLength) {
      return `${label} cannot exceed ${validation.maxLength} characters`;
    }

    if (validation.pattern && !validation.pattern.test(val)) {
      return `${label} format is invalid`;
    }

    if (validation.custom) {
      return validation.custom(val);
    }

    return null;
  }, [label, validation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    if (touched) {
      setError(validateValue(newValue));
    }
  };

  const handleBlur = () => {
    setTouched(true);
    setError(validateValue(value));
  };

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {validation.required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <input
        type={type}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        autoComplete={autoComplete}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors ${
          error
            ? 'border-red-500 bg-red-50'
            : 'border-gray-300 bg-white'
        } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        aria-invalid={!!error}
        aria-describedby={error ? `${label}-error` : undefined}
      />

      {error && (
        <p
          id={`${label}-error`}
          className="text-sm text-red-600"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  );
};
```

### Music Service Connection Components

#### ServiceConnectionCard Component
```typescript
interface ServiceConnectionCardProps {
  service: 'apple' | 'spotify';
  connected: boolean;
  connecting: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
  features: string[];
  requirements: string[];
  recommended?: boolean;
}

const ServiceConnectionCard: React.FC<ServiceConnectionCardProps> = ({
  service,
  connected,
  connecting,
  onConnect,
  onDisconnect,
  features,
  requirements,
  recommended = false,
}) => {
  const serviceConfig = {
    apple: {
      name: 'Apple Music',
      icon: '🍎',
      color: 'from-gray-900 to-gray-700',
      description: 'Full playlist integration with MusicKit',
    },
    spotify: {
      name: 'Spotify',
      icon: '🎵',
      color: 'from-green-600 to-green-400',
      description: 'Song search and metadata validation',
    },
  };

  const config = serviceConfig[service];

  return (
    <div className={`relative p-6 rounded-xl border-2 transition-all duration-200 ${
      connected
        ? 'border-green-500 bg-green-50'
        : recommended
        ? 'border-purple-500 bg-purple-50'
        : 'border-gray-200 bg-white hover:border-gray-300'
    }`}>
      {recommended && (
        <div className="absolute -top-3 left-4 px-3 py-1 bg-purple-500 text-white text-xs font-medium rounded-full">
          Recommended
        </div>
      )}

      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${config.color} flex items-center justify-center text-2xl`}>
            {config.icon}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {config.name}
            </h3>
            <p className="text-sm text-gray-600">
              {config.description}
            </p>
          </div>
        </div>

        {connected && (
          <div className="flex items-center text-green-600">
            <CheckCircle className="w-5 h-5 mr-1" />
            <span className="text-sm font-medium">Connected</span>
          </div>
        )}
      </div>

      <div className="space-y-3 mb-6">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Features:</h4>
          <ul className="space-y-1">
            {features.map((feature, index) => (
              <li key={index} className="flex items-center text-sm text-gray-600">
                <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                {feature}
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Requirements:</h4>
          <ul className="space-y-1">
            {requirements.map((requirement, index) => (
              <li key={index} className="flex items-center text-sm text-gray-600">
                <Info className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" />
                {requirement}
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="flex space-x-3">
        {!connected ? (
          <Button
            onClick={onConnect}
            disabled={connecting}
            className="flex-1"
            variant={recommended ? 'default' : 'outline'}
          >
            {connecting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Connecting...
              </>
            ) : (
              `Connect ${config.name}`
            )}
          </Button>
        ) : (
          <>
            <Button
              onClick={onDisconnect}
              variant="outline"
              className="flex-1"
            >
              Disconnect
            </Button>
            <Button
              onClick={() => {/* Test connection */}}
              variant="ghost"
              size="sm"
            >
              Test
            </Button>
          </>
        )}
      </div>
    </div>
  );
};
```

## 4. Session-Specific Music Service Selection

### Per-Session Service Selection Workflow

When creating a new session, DJs can choose which music service to use for that specific session, allowing for flexibility based on event type, venue requirements, or personal preference.

#### Session Creation with Service Selection

```typescript
interface SessionMusicServiceProps {
  availableServices: {
    apple?: { connected: boolean; lastSync: number };
    spotify?: { connected: boolean; lastSync: number };
  };
  defaultService?: 'apple' | 'spotify' | 'manual';
  onServiceSelect: (service: 'apple' | 'spotify' | 'manual') => void;
  selectedService: 'apple' | 'spotify' | 'manual';
}

const SessionMusicService: React.FC<SessionMusicServiceProps> = ({
  availableServices,
  defaultService,
  onServiceSelect,
  selectedService,
}) => {
  const serviceOptions = [
    {
      id: 'apple' as const,
      name: 'Apple Music',
      icon: '🍎',
      available: availableServices.apple?.connected || false,
      features: [
        'Full playlist integration',
        'Automatic song adding',
        'High-quality previews',
        'Smart duplicate detection',
      ],
      limitations: [],
      recommended: defaultService === 'apple',
    },
    {
      id: 'spotify' as const,
      name: 'Spotify',
      icon: '🎵',
      available: availableServices.spotify?.connected || false,
      features: [
        'Comprehensive song search',
        'Metadata validation',
        'Album artwork',
        'Genre detection',
      ],
      limitations: [
        'Manual playlist management',
        'No automatic song adding',
      ],
      recommended: defaultService === 'spotify',
    },
    {
      id: 'manual' as const,
      name: 'Manual Entry',
      icon: '✏️',
      available: true,
      features: [
        'Always available',
        'No service dependencies',
        'Full control',
      ],
      limitations: [
        'No automatic validation',
        'Manual song entry only',
        'Limited metadata',
        'No playlist integration',
      ],
      recommended: false,
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Music Service for this Session
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {/* Open service settings */}}
        >
          <Settings className="w-4 h-4 mr-2" />
          Manage Services
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        {serviceOptions.map((option) => (
          <div
            key={option.id}
            className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
              selectedService === option.id
                ? 'border-purple-500 bg-purple-50'
                : option.available
                ? 'border-gray-200 bg-white hover:border-gray-300'
                : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-60'
            }`}
            onClick={() => option.available && onServiceSelect(option.id)}
          >
            {option.recommended && (
              <div className="absolute -top-2 -right-2 px-2 py-1 bg-blue-500 text-white text-xs font-medium rounded-full">
                Default
              </div>
            )}

            <div className="flex items-center space-x-3 mb-3">
              <div className="text-2xl">{option.icon}</div>
              <div>
                <h4 className="font-medium text-gray-900">{option.name}</h4>
                {!option.available && option.id !== 'manual' && (
                  <p className="text-xs text-red-600">Not connected</p>
                )}
              </div>
              {selectedService === option.id && (
                <CheckCircle className="w-5 h-5 text-purple-500 ml-auto" />
              )}
            </div>

            <div className="space-y-2">
              {option.features.slice(0, 2).map((feature, index) => (
                <div key={index} className="flex items-center text-sm text-gray-600">
                  <Check className="w-3 h-3 text-green-500 mr-2 flex-shrink-0" />
                  {feature}
                </div>
              ))}

              {option.limitations.length > 0 && (
                <div className="flex items-center text-sm text-amber-600">
                  <AlertTriangle className="w-3 h-3 mr-2 flex-shrink-0" />
                  {option.limitations[0]}
                </div>
              )}
            </div>

            {!option.available && option.id !== 'manual' && (
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-3"
                onClick={(e) => {
                  e.stopPropagation();
                  // Open connection flow
                }}
              >
                Connect {option.name}
              </Button>
            )}
          </div>
        ))}
      </div>

      {selectedService !== 'manual' && (
        <div className="p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">
                {selectedService === 'apple' ? 'Apple Music' : 'Spotify'} Selected
              </p>
              <p>
                {selectedService === 'apple'
                  ? 'Approved songs will be automatically added to your Apple Music playlist.'
                  : 'Songs will be validated against Spotify\'s database for accuracy.'}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="remember-service"
          className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
        />
        <label htmlFor="remember-service" className="text-sm text-gray-600">
          Remember this choice for future sessions
        </label>
      </div>
    </div>
  );
};
```

### Impact of Service Selection on Session Functionality

#### Apple Music Selected
- **Song Search**: Full MusicKit API access with comprehensive catalog
- **Request Validation**: Automatic validation against Apple Music database
- **Playlist Integration**: Approved songs automatically added to Apple Music playlist
- **Preview Playback**: 30-second previews available for DJs
- **Metadata**: High-quality album artwork, genre tags, release information
- **Duplicate Detection**: Smart detection based on Apple Music IDs

#### Spotify Selected
- **Song Search**: Spotify Web API with extensive catalog
- **Request Validation**: Validation against Spotify database with confidence scoring
- **Playlist Integration**: Manual playlist management (no automatic adding)
- **Preview Playback**: 30-second previews where available
- **Metadata**: Album artwork, genre classification, popularity metrics
- **Duplicate Detection**: Based on Spotify track IDs and metadata matching

#### Manual Entry Selected
- **Song Search**: No automatic search (manual entry only)
- **Request Validation**: Basic text validation only
- **Playlist Integration**: None (manual tracking only)
- **Preview Playback**: Not available
- **Metadata**: User-provided information only
- **Duplicate Detection**: Basic text matching

## 5. Implementation Guidelines

### Database Schema Updates

#### DJ Profile Extensions
```typescript
// Convex schema updates for djProfiles table
export const djProfiles = defineTable({
  userId: v.id("users"),
  displayName: v.optional(v.string()),
  bio: v.optional(v.string()),
  venueType: v.optional(v.string()),
  yearsExperience: v.optional(v.string()),
  location: v.optional(v.string()),
  profilePictureStorageId: v.optional(v.id("_storage")),

  // Music service connections
  appleMusicConnected: v.optional(v.boolean()),
  appleMusicToken: v.optional(v.string()),
  appleMusicLastSync: v.optional(v.number()),

  spotifyConnected: v.optional(v.boolean()),
  spotifyAccessToken: v.optional(v.string()),
  spotifyRefreshToken: v.optional(v.string()),
  spotifyLastSync: v.optional(v.number()),

  // Preferences
  defaultMusicService: v.optional(v.union(v.literal("apple"), v.literal("spotify"), v.literal("manual"))),
  fallbackMusicService: v.optional(v.union(v.literal("apple"), v.literal("spotify"), v.literal("manual"))),

  // Session defaults
  defaultSessionDuration: v.optional(v.number()),
  defaultRequestLimit: v.optional(v.number()),
  defaultAutoAccept: v.optional(v.boolean()),
  defaultGenreBlocks: v.optional(v.array(v.string())),

  // Notification preferences
  notificationPreferences: v.optional(v.object({
    newRequests: v.union(v.literal("immediate"), v.literal("batched"), v.literal("off")),
    milestones: v.array(v.number()),
    emailSummary: v.union(v.literal("session"), v.literal("weekly"), v.literal("never")),
  })),

  // Onboarding tracking
  completedOnboarding: v.boolean(),
  onboardingStep: v.optional(v.number()),
  onboardingData: v.optional(v.any()), // Temporary storage for incomplete onboarding

  createdAt: v.number(),
  updatedAt: v.number(),
  lastLoginAt: v.optional(v.number()),
})
.index("by_user", ["userId"])
.index("by_onboarding_status", ["completedOnboarding"]);
```

#### Session Schema Updates
```typescript
// Add music service selection to sessions
export const sessions = defineTable({
  // ... existing fields
  musicService: v.optional(v.union(v.literal("apple"), v.literal("spotify"), v.literal("manual"))),
  musicServiceConfig: v.optional(v.object({
    appleMusicPlaylistId: v.optional(v.string()),
    spotifyPlaylistId: v.optional(v.string()),
    autoAddSongs: v.optional(v.boolean()),
    crossValidation: v.optional(v.boolean()),
  })),
  // ... rest of schema
});
```

### API Endpoints

#### Onboarding Progress Management
```typescript
// convex/onboarding.ts
export const saveOnboardingProgress = mutation({
  args: {
    step: v.number(),
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    await ctx.db.patch(djProfile._id, {
      onboardingStep: args.step,
      onboardingData: args.data,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const getOnboardingProgress = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return djProfile ? {
      step: djProfile.onboardingStep || 1,
      data: djProfile.onboardingData || {},
      completed: djProfile.completedOnboarding,
    } : null;
  },
});
```

#### Music Service Management
```typescript
// convex/musicServices.ts
export const connectAppleMusic = mutation({
  args: {
    userToken: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    await ctx.db.patch(djProfile._id, {
      appleMusicConnected: true,
      appleMusicToken: args.userToken,
      appleMusicLastSync: Date.now(),
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const connectSpotify = mutation({
  args: {
    accessToken: v.string(),
    refreshToken: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    await ctx.db.patch(djProfile._id, {
      spotifyConnected: true,
      spotifyAccessToken: args.accessToken,
      spotifyRefreshToken: args.refreshToken,
      spotifyLastSync: Date.now(),
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});
```

### Component Integration Points

#### Onboarding Router Component
```typescript
// components/onboarding/OnboardingRouter.tsx
const OnboardingRouter: React.FC = () => {
  const { isAuthenticated } = useConvexAuth();
  const userWithProfile = useQuery(api.users.getCurrentUserWithProfile);
  const onboardingProgress = useQuery(api.onboarding.getOnboardingProgress);

  if (!isAuthenticated || !userWithProfile) {
    return <LoadingSpinner />;
  }

  // If onboarding is complete, redirect to dashboard
  if (userWithProfile.djProfile?.completedOnboarding) {
    return <Navigate to="/dashboard" replace />;
  }

  const currentStep = onboardingProgress?.step || 1;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        <ProgressIndicator
          currentStep={currentStep}
          totalSteps={5}
          stepLabels={[
            'Welcome',
            'Profile',
            'Music Service',
            'Preferences',
            'First Session'
          ]}
        />

        <div className="mt-8">
          {currentStep === 1 && <WelcomeScreen />}
          {currentStep === 2 && <ProfileSetup />}
          {currentStep === 3 && <MusicServiceSetup />}
          {currentStep === 4 && <PreferencesSetup />}
          {currentStep === 5 && <FirstSessionCreation />}
        </div>
      </div>
    </div>
  );
};
```

### Mobile Responsiveness Guidelines

#### Breakpoint Strategy
```css
/* Tailwind CSS breakpoints for onboarding */
.onboarding-container {
  @apply px-4 py-6;

  @screen sm {
    @apply px-6 py-8;
  }

  @screen md {
    @apply px-8 py-12;
  }

  @screen lg {
    @apply px-12 py-16;
  }
}

.onboarding-card {
  @apply w-full max-w-md mx-auto;

  @screen md {
    @apply max-w-2xl;
  }

  @screen lg {
    @apply max-w-4xl;
  }
}

.onboarding-form-grid {
  @apply grid grid-cols-1 gap-4;

  @screen md {
    @apply grid-cols-2 gap-6;
  }

  @screen lg {
    @apply gap-8;
  }
}
```

#### Touch Target Guidelines
- Minimum touch target size: 44px x 44px
- Spacing between interactive elements: 8px minimum
- Button padding: 12px vertical, 16px horizontal on mobile
- Form inputs: 48px height on mobile, 40px on desktop

### Accessibility Requirements

#### ARIA Labels and Roles
```typescript
// Example accessible form component
<form role="form" aria-labelledby="profile-setup-title">
  <h2 id="profile-setup-title">DJ Profile Setup</h2>

  <div className="form-group">
    <label htmlFor="dj-name" className="required">
      DJ Display Name
    </label>
    <input
      id="dj-name"
      type="text"
      required
      aria-required="true"
      aria-describedby="dj-name-help dj-name-error"
      aria-invalid={hasError}
    />
    <div id="dj-name-help" className="help-text">
      This name will be visible to your audience
    </div>
    {hasError && (
      <div id="dj-name-error" role="alert" className="error-text">
        Display name is required
      </div>
    )}
  </div>
</form>
```

#### Keyboard Navigation
- Tab order follows logical flow
- All interactive elements focusable
- Skip links for screen readers
- Escape key closes modals/dropdowns
- Enter key submits forms
- Arrow keys navigate radio groups

### Performance Optimization

#### Code Splitting
```typescript
// Lazy load onboarding components
const WelcomeScreen = lazy(() => import('./components/WelcomeScreen'));
const ProfileSetup = lazy(() => import('./components/ProfileSetup'));
const MusicServiceSetup = lazy(() => import('./components/MusicServiceSetup'));

// Preload next step component
const preloadNextStep = (currentStep: number) => {
  const nextComponents = {
    1: () => import('./components/ProfileSetup'),
    2: () => import('./components/MusicServiceSetup'),
    3: () => import('./components/PreferencesSetup'),
    4: () => import('./components/FirstSessionCreation'),
  };

  const preloader = nextComponents[currentStep as keyof typeof nextComponents];
  if (preloader) {
    preloader();
  }
};
```

#### Image Optimization
- Profile pictures: WebP format with JPEG fallback
- Maximum upload size: 5MB
- Automatic resizing to 400x400px
- Progressive loading with blur placeholder
- Lazy loading for non-critical images

This completes the comprehensive DJ onboarding design with detailed implementation guidelines, component specifications, and technical requirements.
