/**
 * Spotify OAuth Integration for PlayBeg DJ Onboarding
 * 
 * This module handles Spotify OAuth authentication using PKCE flow
 * for secure client-side authentication without exposing client secrets.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api } from "./_generated/api";

// Spotify OAuth configuration
const SPOTIFY_CLIENT_ID = process.env.SPOTIFY_CLIENT_ID;
const SPOTIFY_CLIENT_SECRET = process.env.SPOTIFY_CLIENT_SECRET;
const SPOTIFY_REDIRECT_URI = process.env.SPOTIFY_REDIRECT_URI || "http://localhost:3000/auth/spotify-callback";

// Required Spotify scopes for PlayBeg functionality
const SPOTIFY_SCOPES = [
  'user-read-private',
  'user-read-email',
  'playlist-modify-public',
  'playlist-modify-private',
  'playlist-read-private',
  'user-library-read',
  'user-library-modify'
].join(' ');

// PKCE helper functions
function generateRandomString(length: number): string {
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const values = crypto.getRandomValues(new Uint8Array(length));
  return values.reduce((acc, x) => acc + possible[x % possible.length], "");
}

async function sha256(plain: string): Promise<ArrayBuffer> {
  const encoder = new TextEncoder();
  const data = encoder.encode(plain);
  return await crypto.subtle.digest('SHA-256', data);
}

function base64encode(input: ArrayBuffer): string {
  const bytes = new Uint8Array(input);
  const binary = Array.from(bytes, byte => String.fromCharCode(byte)).join('');
  return btoa(binary)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Initiate Spotify OAuth flow with PKCE
 * Generates authorization URL and stores state/verifier for security
 */
export const initiateSpotifyAuth = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    if (!SPOTIFY_CLIENT_ID) {
      throw new Error("Spotify Client ID not configured");
    }

    // Generate PKCE parameters
    const codeVerifier = generateRandomString(64);
    const hashed = await sha256(codeVerifier);
    const codeChallenge = base64encode(hashed);
    const state = generateRandomString(16);

    // Store PKCE parameters for verification
    await ctx.db.insert("spotify_oauth_states", {
      userId,
      state,
      codeVerifier,
      codeChallenge,
      createdAt: Date.now(),
      expiresAt: Date.now() + (10 * 60 * 1000), // 10 minutes
    });

    // Build authorization URL
    const params = new URLSearchParams({
      client_id: SPOTIFY_CLIENT_ID,
      response_type: 'code',
      redirect_uri: SPOTIFY_REDIRECT_URI,
      state: state,
      scope: SPOTIFY_SCOPES,
      code_challenge_method: 'S256',
      code_challenge: codeChallenge,
    });

    const authUrl = `https://accounts.spotify.com/authorize?${params.toString()}`;

    return {
      authUrl,
      state,
      success: true,
    };
  },
});

/**
 * Handle Spotify OAuth callback
 * Exchanges authorization code for access tokens
 */
export const handleSpotifyCallback = mutation({
  args: {
    code: v.string(),
    state: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    if (!SPOTIFY_CLIENT_ID || !SPOTIFY_CLIENT_SECRET) {
      throw new Error("Spotify credentials not configured");
    }

    // Verify state parameter
    const oauthState = await ctx.db
      .query("spotify_oauth_states")
      .withIndex("by_user_state", (q) => 
        q.eq("userId", userId).eq("state", args.state)
      )
      .first();

    if (!oauthState) {
      throw new Error("Invalid or expired OAuth state");
    }

    if (oauthState.expiresAt < Date.now()) {
      throw new Error("OAuth state expired");
    }

    try {
      // Exchange code for tokens
      const tokenResponse = await fetch('https://accounts.spotify.com/api/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${btoa(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`)}`,
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code: args.code,
          redirect_uri: SPOTIFY_REDIRECT_URI,
          code_verifier: oauthState.codeVerifier,
        }),
      });

      if (!tokenResponse.ok) {
        const error = await tokenResponse.text();
        throw new Error(`Token exchange failed: ${error}`);
      }

      const tokens = await tokenResponse.json();

      // Get user profile from Spotify
      const profileResponse = await fetch('https://api.spotify.com/v1/me', {
        headers: {
          'Authorization': `Bearer ${tokens.access_token}`,
        },
      });

      if (!profileResponse.ok) {
        throw new Error('Failed to fetch Spotify profile');
      }

      const profile = await profileResponse.json();

      // Store tokens in database
      const expiresAt = Date.now() + (tokens.expires_in * 1000);
      
      await ctx.db.insert("spotify_tokens", {
        userId,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiresAt,
        spotifyUserId: profile.id,
        spotifyEmail: profile.email,
        spotifyDisplayName: profile.display_name,
        isValid: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Update DJ profile with Spotify connection
      const djProfile = await ctx.db
        .query("djProfiles")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first();

      if (djProfile) {
        // Disconnect any existing music service (single service enforcement)
        await ctx.db.patch(djProfile._id, {
          connectedMusicService: "spotify",
          musicServiceData: {
            spotifyUserId: profile.id,
            spotifyDisplayName: profile.display_name,
            spotifyEmail: profile.email,
            spotifyLastSync: Date.now(),
          },
          updatedAt: Date.now(),
        });
      }

      // Clean up OAuth state
      await ctx.db.delete(oauthState._id);

      return {
        success: true,
        profile: {
          id: profile.id,
          displayName: profile.display_name,
          email: profile.email,
        },
      };

    } catch (error) {
      // Clean up OAuth state on error
      await ctx.db.delete(oauthState._id);
      throw error;
    }
  },
});

/**
 * Refresh Spotify access token
 */
export const refreshSpotifyToken = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const tokenRecord = await ctx.db
      .query("spotify_tokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isValid"), true))
      .first();

    if (!tokenRecord || !tokenRecord.refreshToken) {
      throw new Error("No valid Spotify refresh token found");
    }

    if (!SPOTIFY_CLIENT_ID || !SPOTIFY_CLIENT_SECRET) {
      throw new Error("Spotify credentials not configured");
    }

    try {
      const response = await fetch('https://accounts.spotify.com/api/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${btoa(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`)}`,
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: tokenRecord.refreshToken,
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Token refresh failed: ${error}`);
      }

      const tokens = await response.json();
      const expiresAt = Date.now() + (tokens.expires_in * 1000);

      // Update token record
      await ctx.db.patch(tokenRecord._id, {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token || tokenRecord.refreshToken, // Keep old refresh token if not provided
        expiresAt,
        updatedAt: Date.now(),
      });

      return {
        success: true,
        accessToken: tokens.access_token,
        expiresAt,
      };

    } catch (error) {
      // Mark token as invalid on refresh failure
      await ctx.db.patch(tokenRecord._id, {
        isValid: false,
        updatedAt: Date.now(),
      });
      throw error;
    }
  },
});

/**
 * Disconnect Spotify account
 */
export const disconnectSpotify = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    // Mark all tokens as invalid
    const tokens = await ctx.db
      .query("spotify_tokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    for (const token of tokens) {
      await ctx.db.patch(token._id, {
        isValid: false,
        updatedAt: Date.now(),
      });
    }

    // Update DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (djProfile && djProfile.connectedMusicService === "spotify") {
      await ctx.db.patch(djProfile._id, {
        connectedMusicService: null,
        musicServiceData: null,
        updatedAt: Date.now(),
      });
    }

    return { success: true };
  },
});

/**
 * Get current Spotify connection status
 */
export const getSpotifyConnectionStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return { connected: false };

    const tokenRecord = await ctx.db
      .query("spotify_tokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isValid"), true))
      .first();

    if (!tokenRecord) {
      return { connected: false };
    }

    const isExpired = tokenRecord.expiresAt < Date.now();
    
    return {
      connected: !isExpired,
      profile: {
        id: tokenRecord.spotifyUserId,
        displayName: tokenRecord.spotifyDisplayName,
        email: tokenRecord.spotifyEmail,
      },
      expiresAt: tokenRecord.expiresAt,
      needsRefresh: isExpired,
    };
  },
});

/**
 * Get valid Spotify access token (with auto-refresh)
 */
export const getSpotifyAccessToken = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const tokenRecord = await ctx.db
      .query("spotify_tokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isValid"), true))
      .first();

    if (!tokenRecord) {
      throw new Error("No Spotify connection found");
    }

    // Check if token needs refresh
    if (tokenRecord.expiresAt < Date.now() + (5 * 60 * 1000)) { // Refresh 5 minutes early
      const refreshResult = await ctx.runMutation(api.spotify_auth.refreshSpotifyToken, {});
      return {
        accessToken: refreshResult.accessToken,
        expiresAt: refreshResult.expiresAt,
      };
    }

    return {
      accessToken: tokenRecord.accessToken,
      expiresAt: tokenRecord.expiresAt,
    };
  },
});
