# Apple Music Integration - Convex Migration Setup

This guide explains how to complete the Apple Music integration migration from Supabase to Convex.

## Overview

The Apple Music integration has been migrated from Supabase Edge Functions to pure Convex architecture, matching the Spotify integration pattern. This provides:

- ✅ **Architectural Consistency**: Both Apple Music and Spotify use pure Convex
- ✅ **Simplified Deployment**: No Supabase dependencies
- ✅ **Environment Management**: Centralized in Convex
- ✅ **Better Performance**: Direct Convex queries instead of HTTP calls

## Prerequisites

1. **Apple Developer Account** ($99/year)
2. **MusicKit Identifier** created in Apple Developer Portal
3. **Private Key (.p8 file)** generated for MusicKit
4. **Team ID** and **Key ID** from Apple Developer Portal

## Step 1: Generate Apple Music Developer Token

### 1.1 Create MusicKit Identifier

1. Go to [Apple Developer Portal](https://developer.apple.com/account/)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Click **Identifiers** → **+** (Add new)
4. Select **MusicKit Identifier**
5. Enter details:
   - **Description**: PlayBeg DJ Platform
   - **Identifier**: `com.playbeg.musickit` (or your preferred identifier)
6. Click **Continue** and **Register**

### 1.2 Generate Private Key

1. In Apple Developer Portal, go to **Keys**
2. Click **+** (Add new key)
3. Enter **Key Name**: PlayBeg MusicKit Key
4. Check **MusicKit** checkbox
5. Click **Continue** and **Register**
6. **Download the .p8 file** (you can only download once!)
7. Note the **Key ID** (10-character string)

### 1.3 Get Team ID

1. In Apple Developer Portal, go to **Membership**
2. Note your **Team ID** (10-character string)

### 1.4 Generate JWT Token

You need to create a JWT token using the ES256 algorithm. Here's a Node.js script:

```javascript
// generate-apple-music-token.js
const jwt = require('jsonwebtoken');
const fs = require('fs');

// Your Apple Developer details
const TEAM_ID = 'YOUR_TEAM_ID';        // 10-character Team ID
const KEY_ID = 'YOUR_KEY_ID';          // 10-character Key ID
const PRIVATE_KEY_PATH = './AuthKey_YOUR_KEY_ID.p8'; // Path to your .p8 file

// Read the private key
const privateKey = fs.readFileSync(PRIVATE_KEY_PATH, 'utf8');

// JWT payload
const payload = {
  iss: TEAM_ID,
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (6 * 30 * 24 * 60 * 60), // 6 months
};

// JWT header
const header = {
  alg: 'ES256',
  kid: KEY_ID,
};

// Generate the token
const token = jwt.sign(payload, privateKey, { 
  algorithm: 'ES256',
  header: header 
});

console.log('Apple Music Developer Token:');
console.log(token);
```

Run the script:
```bash
npm install jsonwebtoken
node generate-apple-music-token.js
```

## Step 2: Configure Convex Environment

### 2.1 Set Environment Variable

Set the generated JWT token in your Convex deployment:

```bash
# For development deployment (dev:lovely-cormorant-474)
npx convex env set APPLE_MUSIC_DEVELOPER_TOKEN "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9..."

# Verify it was set
npx convex env list
```

### 2.2 For Production Deployment

When you create a production deployment:

```bash
# Create production deployment
npx convex deploy --prod

# Set production environment variable
npx convex env set APPLE_MUSIC_DEVELOPER_TOKEN "your_token_here" --prod
```

## Step 3: Test the Integration

### 3.1 Verify Convex Query

Test that the Convex query returns the token:

```bash
# In your development environment
npx convex dev

# In another terminal, test the query
npx convex run appleMusicTokens:getAppleMusicDeveloperToken
```

Expected output:
```json
{
  "token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9...",
  "source": "convex-environment",
  "expiresIn": 15552000
}
```

### 3.2 Test in Application

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to DJ onboarding: `http://localhost:3000/dj-onboarding`

3. In Step 3 (Music Service Setup), select **Apple Music**

4. Check browser console for logs:
   ```
   ✅ Successfully retrieved Apple Music Developer Token from Convex
   ✅ Apple Music authorization successful
   ```

## Step 4: Verify Migration Completion

### 4.1 Check for Supabase References

Ensure no Supabase references remain:

```bash
# Search for any remaining Supabase references in Apple Music code
grep -r "supabase" src/services/AppleMusicTokenService.ts
grep -r "supabase.functions" src/hooks/useAppleMusicAuth.ts

# Should return no results
```

### 4.2 Architecture Verification

The Apple Music integration should now follow the same pattern as Spotify:

| Component | Apple Music | Spotify | Status |
|-----------|-------------|---------|---------|
| **Backend** | ✅ Pure Convex | ✅ Pure Convex | ✅ Consistent |
| **Token Storage** | ✅ Convex DB | ✅ Convex DB | ✅ Consistent |
| **Environment** | ✅ Convex Env | ✅ Convex Env | ✅ Consistent |
| **No Supabase** | ✅ Migrated | ✅ Never used | ✅ Clean |

## Step 5: Token Management

### 5.1 Token Expiration

Apple Music Developer Tokens are long-lived (up to 6 months). The current implementation:

- **Caches tokens** for 1 hour to reduce Convex queries
- **Automatically retrieves** from Convex environment when cache expires
- **Handles errors** gracefully with clear error messages

### 5.2 Token Rotation

When your token expires (every 6 months):

1. Generate a new JWT token using the same process
2. Update the Convex environment variable:
   ```bash
   npx convex env set APPLE_MUSIC_DEVELOPER_TOKEN "new_token_here"
   ```
3. Clear the application cache (tokens are cached for 1 hour)

## Troubleshooting

### Common Issues

1. **"Apple Music Developer Token not configured"**
   - Verify the environment variable is set: `npx convex env list`
   - Check the token format (should start with `eyJ`)

2. **"Convex client not initialized"**
   - Ensure `useAppleMusicAuth` hook is used within a Convex provider
   - Check that the Convex client is properly configured

3. **JWT Token Invalid**
   - Verify Team ID, Key ID, and private key are correct
   - Check token expiration date
   - Ensure ES256 algorithm is used

4. **MusicKit Authorization Fails**
   - Verify MusicKit identifier is correctly configured
   - Check that the user has an active Apple Music subscription
   - Ensure the domain is authorized for MusicKit usage

### Debug Mode

Enable debug logging:

```javascript
// In browser console
localStorage.setItem('debug', 'apple-music:*');
```

This will show detailed logs for Apple Music token retrieval and authorization.

## Security Notes

- **Never commit** private keys (.p8 files) to version control
- **Rotate tokens** every 6 months or when compromised
- **Use environment variables** for all sensitive data
- **Monitor token usage** for unusual activity

## Migration Complete ✅

The Apple Music integration is now fully migrated to Convex and follows the same architectural patterns as the Spotify integration. Both services now use:

- ✅ **Pure Convex Backend**: No Supabase dependencies
- ✅ **Convex Environment Variables**: Centralized credential management
- ✅ **Consistent Architecture**: Same patterns for both services
- ✅ **Production Ready**: Secure, scalable, and maintainable

The PlayBeg DJ onboarding now has complete architectural consistency across all music service integrations!
