# Spotify Integration Setup Guide

This guide explains how to set up Spotify integration for PlayBeg DJ onboarding.

## Prerequisites

1. **Spotify Developer Account**: You need a Spotify Developer account to create an application
2. **Spotify Application**: Create a new application in the Spotify Developer Dashboard
3. **Environment Variables**: Configure the required environment variables

## Step 1: Create Spotify Application

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Click "Create App"
3. Fill in the application details:
   - **App Name**: PlayBeg DJ Platform
   - **App Description**: DJ request platform with Spotify integration
   - **Website**: Your PlayBeg domain
   - **Redirect URI**: `https://yourdomain.com/auth/spotify-callback` (production) or `http://localhost:3000/auth/spotify-callback` (development)
4. Accept the terms and create the app
5. Note down your **Client ID** and **Client Secret**

## Step 2: Configure Redirect URIs

In your Spotify app settings, add the following redirect URIs:

### Development
```
http://localhost:3000/auth/spotify-callback
http://127.0.0.1:3000/auth/spotify-callback
```

### Production
```
https://yourdomain.com/auth/spotify-callback
```

## Step 3: Environment Variables

Add the following environment variables to your `.env.local` file:

```env
# Spotify OAuth Configuration
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_REDIRECT_URI=http://localhost:3000/auth/spotify-callback

# For production, use your production domain:
# SPOTIFY_REDIRECT_URI=https://yourdomain.com/auth/spotify-callback
```

## Step 4: Spotify API Scopes

The PlayBeg integration requests the following Spotify scopes:

- `user-read-private`: Access to user's subscription details and country
- `user-read-email`: Access to user's email address
- `playlist-modify-public`: Create and modify public playlists
- `playlist-modify-private`: Create and modify private playlists
- `playlist-read-private`: Read user's private playlists
- `user-library-read`: Read user's saved tracks
- `user-library-modify`: Modify user's saved tracks

## Step 5: Database Schema

The Spotify integration uses the following database tables (automatically created by Convex):

### `spotifyTokens`
- `userId`: Reference to the user
- `accessToken`: Spotify access token
- `refreshToken`: Spotify refresh token
- `expiresAt`: Token expiration timestamp
- `spotifyUserId`: User's Spotify ID
- `spotifyEmail`: User's Spotify email
- `spotifyDisplayName`: User's Spotify display name
- `isValid`: Token validity flag
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp

### `spotifyOauthStates`
- `userId`: Reference to the user
- `state`: OAuth state parameter
- `codeVerifier`: PKCE code verifier
- `codeChallenge`: PKCE code challenge
- `createdAt`: Creation timestamp
- `expiresAt`: State expiration timestamp

## Step 6: Testing the Integration

### Development Testing

1. Start your development server
2. Navigate to the DJ onboarding flow
3. Select "Spotify" in the music service setup step
4. You should be redirected to Spotify for authorization
5. After authorization, you'll be redirected back to complete onboarding

### Test Cases

- **Successful Connection**: User authorizes and connects successfully
- **Access Denied**: User declines authorization
- **Invalid State**: Tampered or expired OAuth state
- **Token Refresh**: Automatic token refresh when expired
- **Error Handling**: Network errors, invalid responses

## Step 7: Production Deployment

### Environment Variables
Ensure all production environment variables are set:

```env
SPOTIFY_CLIENT_ID=your_production_client_id
SPOTIFY_CLIENT_SECRET=your_production_client_secret
SPOTIFY_REDIRECT_URI=https://yourdomain.com/auth/spotify-callback
```

### Security Considerations

1. **HTTPS Only**: Always use HTTPS in production
2. **Secure Storage**: Client secrets should be stored securely
3. **Token Encryption**: Consider encrypting stored tokens
4. **Rate Limiting**: Implement rate limiting for OAuth endpoints
5. **CORS Configuration**: Properly configure CORS for your domain

## Step 8: Monitoring and Maintenance

### Token Management
- Tokens are automatically refreshed when they expire
- Invalid tokens are marked and require re-authorization
- Users can disconnect and reconnect their Spotify accounts

### Error Monitoring
Monitor the following potential issues:
- OAuth callback failures
- Token refresh failures
- Spotify API rate limits
- Network connectivity issues

### User Experience
- Clear error messages for common issues
- Graceful fallback to manual entry
- Retry mechanisms for temporary failures

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**
   - Check that your redirect URI exactly matches what's configured in Spotify
   - Ensure no trailing slashes or extra parameters

2. **"Invalid client"**
   - Verify your Client ID and Client Secret are correct
   - Check that your Spotify app is not in development mode restrictions

3. **"Access denied"**
   - User declined authorization - this is normal user behavior
   - Provide option to continue with manual entry

4. **Token refresh failures**
   - Check that refresh tokens are being stored correctly
   - Verify your Client Secret is correct for token refresh

### Debug Mode

Enable debug logging by setting:
```env
DEBUG_SPOTIFY_AUTH=true
```

This will log OAuth flow details to help troubleshoot issues.

## API Usage Guidelines

### Rate Limits
- Spotify API has rate limits (typically 100 requests per minute)
- Implement exponential backoff for rate limit errors
- Cache responses when appropriate

### Best Practices
- Always check token expiration before API calls
- Handle 401 errors by refreshing tokens
- Provide meaningful error messages to users
- Log errors for monitoring and debugging

## Support

For issues with the Spotify integration:

1. Check the Spotify Developer Documentation
2. Review the PlayBeg integration logs
3. Test with the Spotify Web API Console
4. Contact Spotify Developer Support if needed

## Security Notes

- Never expose Client Secrets in client-side code
- Use PKCE flow for enhanced security
- Regularly rotate Client Secrets
- Monitor for suspicious OAuth activity
- Implement proper session management
