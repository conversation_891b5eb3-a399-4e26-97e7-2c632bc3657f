/**
 * Apple Music Token Management
 * 
 * This module handles all database operations for Apple Music tokens including
 * creation, reading, updating, deletion, and automatic refresh with secure
 * storage and expiration handling.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get current user's Apple Music token
export const getCurrentUserAppleMusicToken = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const token = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!token) {
      return null;
    }

    return {
      id: token._id,
      userId: token.userId,
      isValid: token.isValid,
      expiresAt: token.expiresAt,
      createdAt: token.createdAt,
      updatedAt: token.updatedAt,
      isExpired: isTokenExpired(token.expiresAt),
      expiresInHours: getHoursUntilExpiration(token.expiresAt),
    };
  },
});

// Query: Check if user has valid Apple Music token
export const hasValidAppleMusicToken = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        hasToken: false,
        isValid: false,
        isExpired: true,
      };
    }

    const token = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user_valid", (q) => 
        q.eq("userId", userId).eq("isValid", true)
      )
      .first();

    if (!token) {
      return {
        hasToken: false,
        isValid: false,
        isExpired: true,
      };
    }

    const expired = isTokenExpired(token.expiresAt);
    
    return {
      hasToken: true,
      isValid: token.isValid && !expired,
      isExpired: expired,
      expiresAt: token.expiresAt,
      expiresInHours: getHoursUntilExpiration(token.expiresAt),
    };
  },
});

// Query: Get Apple Music token for API use (internal)
export const getAppleMusicTokenForApi = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const token = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user_valid", (q) => 
        q.eq("userId", userId).eq("isValid", true)
      )
      .first();

    if (!token) {
      throw new Error("No valid Apple Music token found");
    }

    if (isTokenExpired(token.expiresAt)) {
      throw new Error("Apple Music token has expired");
    }

    // Return the actual token for API use
    // Note: In production, consider additional security measures
    return {
      token: token.appleMusicToken,
      expiresAt: token.expiresAt,
    };
  },
});

// Mutation: Store Apple Music token
export const storeAppleMusicToken = mutation({
  args: {
    appleMusicToken: v.string(),
    expiresAt: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to store Apple Music token");
    }

    // Validate token format (basic validation)
    if (args.appleMusicToken.trim().length === 0) {
      throw new Error("Apple Music token cannot be empty");
    }

    // Validate expiration time
    const now = Date.now();
    if (args.expiresAt <= now) {
      throw new Error("Token expiration time must be in the future");
    }

    // Check if token already exists
    const existingToken = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (existingToken) {
      // Update existing token
      await ctx.db.patch(existingToken._id, {
        appleMusicToken: args.appleMusicToken,
        expiresAt: args.expiresAt,
        isValid: true,
        updatedAt: now,
      });

      return {
        tokenId: existingToken._id,
        message: "Apple Music token updated successfully",
        isNew: false,
      };
    } else {
      // Create new token
      const tokenId = await ctx.db.insert("appleMusicTokens", {
        userId,
        appleMusicToken: args.appleMusicToken,
        expiresAt: args.expiresAt,
        isValid: true,
        createdAt: now,
        updatedAt: now,
      });

      return {
        tokenId,
        message: "Apple Music token stored successfully",
        isNew: true,
      };
    }
  },
});

// Mutation: Refresh Apple Music token
export const refreshAppleMusicToken = mutation({
  args: {
    newAppleMusicToken: v.string(),
    newExpiresAt: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const existingToken = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!existingToken) {
      throw new Error("No existing Apple Music token found to refresh");
    }

    // Validate new token
    if (args.newAppleMusicToken.trim().length === 0) {
      throw new Error("New Apple Music token cannot be empty");
    }

    const now = Date.now();
    if (args.newExpiresAt <= now) {
      throw new Error("New token expiration time must be in the future");
    }

    await ctx.db.patch(existingToken._id, {
      appleMusicToken: args.newAppleMusicToken,
      expiresAt: args.newExpiresAt,
      isValid: true,
      updatedAt: now,
    });

    return {
      tokenId: existingToken._id,
      message: "Apple Music token refreshed successfully",
      expiresAt: args.newExpiresAt,
      expiresInHours: getHoursUntilExpiration(args.newExpiresAt),
    };
  },
});

// Mutation: Invalidate Apple Music token
export const invalidateAppleMusicToken = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const token = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!token) {
      return {
        success: true,
        message: "No Apple Music token found to invalidate",
      };
    }

    await ctx.db.patch(token._id, {
      isValid: false,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: "Apple Music token invalidated successfully",
    };
  },
});

// Mutation: Delete Apple Music token
export const deleteAppleMusicToken = mutation({
  args: {
    confirmDelete: v.boolean(),
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Delete confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const token = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!token) {
      return {
        success: true,
        message: "No Apple Music token found to delete",
      };
    }

    await ctx.db.delete(token._id);

    return {
      success: true,
      message: "Apple Music token deleted successfully",
    };
  },
});

// Query: Get expired tokens (for cleanup/maintenance)
export const getExpiredTokens = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check when admin system is implemented
    // For now, any authenticated user can view expired tokens for development

    const limit = args.limit ?? 50;
    const now = Date.now();

    const expiredTokens = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_expires", (q) => q.lt("expiresAt", now))
      .take(limit);

    return expiredTokens.map(token => ({
      id: token._id,
      userId: token.userId,
      isValid: token.isValid,
      expiresAt: token.expiresAt,
      expiredHoursAgo: Math.floor((now - token.expiresAt) / (60 * 60 * 1000)),
      createdAt: token.createdAt,
      updatedAt: token.updatedAt,
    }));
  },
});

// Mutation: Cleanup expired tokens (maintenance)
export const cleanupExpiredTokens = mutation({
  args: {
    olderThanDays: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check when admin system is implemented

    const olderThanDays = args.olderThanDays ?? 30; // Default 30 days
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);

    const expiredTokens = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_expires", (q) => q.lt("expiresAt", cutoffTime))
      .collect();

    let deletedCount = 0;
    for (const token of expiredTokens) {
      await ctx.db.delete(token._id);
      deletedCount++;
    }

    return {
      success: true,
      deletedCount,
      message: `Cleaned up ${deletedCount} expired tokens older than ${olderThanDays} days`,
    };
  },
});

/**
 * Get Apple Music Developer Token from Convex environment
 * This replaces the Supabase Edge Function approach
 */
export const getAppleMusicDeveloperToken = query({
  args: {},
  handler: async (ctx) => {
    // Get the developer token from Convex environment variables
    const developerToken = process.env.APPLE_MUSIC_DEVELOPER_TOKEN;

    if (!developerToken) {
      throw new Error("Apple Music Developer Token not configured in Convex environment");
    }

    // Return the token with a long expiration (Apple Music developer tokens are long-lived)
    return {
      token: developerToken,
      source: 'convex-environment',
      expiresIn: 3 * 30 * 24 * 60 * 60, // 3 months in seconds
    };
  },
});

/**
 * Get valid Apple Music access token (with auto-refresh)
 */
export const getAppleMusicAccessToken = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const tokenRecord = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user_valid", (q) =>
        q.eq("userId", userId).eq("isValid", true)
      )
      .first();

    if (!tokenRecord) {
      throw new Error("No Apple Music connection found");
    }

    // Check if token needs refresh (Apple Music tokens typically last 24 hours)
    if (tokenRecord.expiresAt < Date.now() + (5 * 60 * 1000)) { // Refresh 5 minutes early
      // For Apple Music, we would need to implement token refresh logic
      // This depends on the specific Apple Music API refresh mechanism
      throw new Error("Apple Music token expired and refresh not yet implemented");
    }

    return {
      accessToken: tokenRecord.appleMusicToken,
      expiresAt: tokenRecord.expiresAt,
    };
  },
});

// Helper function to check if token is expired
function isTokenExpired(expiresAt: number): boolean {
  return Date.now() >= expiresAt;
}

// Helper function to get hours until expiration
function getHoursUntilExpiration(expiresAt: number): number {
  const msUntilExpiration = expiresAt - Date.now();
  if (msUntilExpiration <= 0) return 0;
  return Math.floor(msUntilExpiration / (60 * 60 * 1000));
}
