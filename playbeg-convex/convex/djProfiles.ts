/**
 * DJ Profile CRUD Operations
 * 
 * This module handles all database operations for DJ profiles including
 * creation, reading, updating, and deletion with proper authentication
 * and validation.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { ValidationError, throwIfInvalid } from "./validation";
// Note: Error handling will be implemented in a future iteration
// For now, using basic error handling

// Query: Get DJ profile by user ID
export const getDjProfileByUserId = query({
  args: {
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    // If no userId provided, use current authenticated user
    const userId = args.userId ?? await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const profile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return profile;
  },
});

// Query: Get current user's DJ profile
export const getCurrentDjProfile = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const profile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return profile;
  },
});

// Query: Get DJ profile by ID (with ownership check)
export const getDjProfileById = query({
  args: {
    profileId: v.id("djProfiles"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const profile = await ctx.db.get(args.profileId);
    if (!profile) {
      return null;
    }

    // Check ownership
    if (profile.userId !== userId) {
      throw new Error("Access denied: You can only access your own profile");
    }

    return profile;
  },
});

// Query: List DJ profiles (for admin/public use)
export const listDjProfiles = query({
  args: {
    limit: v.optional(v.number()),
    onboardingStatus: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;

    let profiles;

    // Filter by onboarding status if specified
    if (args.onboardingStatus !== undefined) {
      profiles = await ctx.db
        .query("djProfiles")
        .withIndex("by_onboarding", (q) =>
          q.eq("completedOnboarding", args.onboardingStatus!)
        )
        .take(limit);
    } else {
      profiles = await ctx.db
        .query("djProfiles")
        .order("desc")
        .take(limit);
    }

    // Return public information only
    return profiles.map(profile => ({
      id: profile._id,
      displayName: profile.displayName,
      completedOnboarding: profile.completedOnboarding,
      createdAt: profile.createdAt,
    }));
  },
});

// Query: Get DJ profiles with recent activity
export const getRecentlyActiveDjProfiles = query({
  args: {
    limit: v.optional(v.number()),
    daysSince: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    const daysSince = args.daysSince ?? 30;
    const cutoffTime = Date.now() - (daysSince * 24 * 60 * 60 * 1000);

    const profiles = await ctx.db
      .query("djProfiles")
      .withIndex("by_last_login")
      .filter((q) => 
        q.and(
          q.neq(q.field("lastLoginAt"), undefined),
          q.gte(q.field("lastLoginAt"), cutoffTime)
        )
      )
      .order("desc")
      .take(limit);

    return profiles.map(profile => ({
      id: profile._id,
      displayName: profile.displayName,
      lastLoginAt: profile.lastLoginAt,
      completedOnboarding: profile.completedOnboarding,
    }));
  },
});

// Mutation: Create DJ profile
export const createDjProfile = mutation({
  args: {
    displayName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to create DJ profile");
    }

    // Check if profile already exists
    const existingProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (existingProfile) {
      throw new Error("DJ profile already exists for this user");
    }

    // Validate display name if provided
    if (args.displayName !== undefined) {
      if (args.displayName.trim().length === 0) {
        throw new ValidationError(["Display name cannot be empty"]);
      }
      if (args.displayName.length > 100) {
        throw new ValidationError(["Display name cannot exceed 100 characters"]);
      }
    }

    const now = Date.now();
    const profileId = await ctx.db.insert("djProfiles", {
      userId,
      displayName: args.displayName?.trim(),
      completedOnboarding: false,
      createdAt: now,
      updatedAt: now,
    });

    return profileId;
  },
});

// Mutation: Update DJ profile (Enhanced for onboarding)
export const updateDjProfile = mutation({
  args: {
    profileId: v.id("djProfiles"),
    displayName: v.optional(v.string()),
    completedOnboarding: v.optional(v.boolean()),
    profilePictureStorageId: v.optional(v.id("_storage")),

    // Enhanced profile fields
    bio: v.optional(v.string()),
    venueType: v.optional(v.string()),
    yearsExperience: v.optional(v.string()),
    location: v.optional(v.string()),

    // Music service fields
    connectedMusicService: v.optional(v.union(
      v.literal("apple"),
      v.literal("spotify"),
      v.literal("manual")
    )),
    musicServiceData: v.optional(v.object({
      appleMusicToken: v.optional(v.string()),
      appleMusicLastSync: v.optional(v.number()),
      spotifyAccessToken: v.optional(v.string()),
      spotifyRefreshToken: v.optional(v.string()),
      spotifyLastSync: v.optional(v.number()),
    })),

    // Session defaults
    defaultSessionDuration: v.optional(v.number()),
    defaultRequestLimit: v.optional(v.number()),
    defaultRequestHandling: v.optional(v.union(
      v.literal("ask"),
      v.literal("auto-accept"),
      v.literal("auto-deny")
    )),
    defaultGenreBlocks: v.optional(v.array(v.string())),
    defaultNotifications: v.optional(v.union(
      v.literal("immediate"),
      v.literal("batched"),
      v.literal("off")
    )),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get existing profile and verify ownership
    const existingProfile = await ctx.db.get(args.profileId);
    if (!existingProfile) {
      throw new Error("DJ profile not found");
    }

    if (existingProfile.userId !== userId) {
      throw new Error("Access denied: You can only update your own profile");
    }

    // Validate display name if provided
    if (args.displayName !== undefined) {
      if (args.displayName.trim().length === 0) {
        throw new ValidationError(["Display name cannot be empty"]);
      }
      if (args.displayName.length > 100) {
        throw new ValidationError(["Display name cannot exceed 100 characters"]);
      }
    }

    // Prepare update object
    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Basic fields
    if (args.displayName !== undefined) updateData.displayName = args.displayName.trim();
    if (args.completedOnboarding !== undefined) updateData.completedOnboarding = args.completedOnboarding;
    if (args.profilePictureStorageId !== undefined) updateData.profilePictureStorageId = args.profilePictureStorageId;

    // Enhanced profile fields
    if (args.bio !== undefined) updateData.bio = args.bio;
    if (args.venueType !== undefined) updateData.venueType = args.venueType;
    if (args.yearsExperience !== undefined) updateData.yearsExperience = args.yearsExperience;
    if (args.location !== undefined) updateData.location = args.location;

    // Music service fields
    if (args.connectedMusicService !== undefined) updateData.connectedMusicService = args.connectedMusicService;
    if (args.musicServiceData !== undefined) updateData.musicServiceData = args.musicServiceData;

    // Session defaults
    if (args.defaultSessionDuration !== undefined) updateData.defaultSessionDuration = args.defaultSessionDuration;
    if (args.defaultRequestLimit !== undefined) updateData.defaultRequestLimit = args.defaultRequestLimit;
    if (args.defaultRequestHandling !== undefined) updateData.defaultRequestHandling = args.defaultRequestHandling;
    if (args.defaultGenreBlocks !== undefined) updateData.defaultGenreBlocks = args.defaultGenreBlocks;
    if (args.defaultNotifications !== undefined) updateData.defaultNotifications = args.defaultNotifications;

    await ctx.db.patch(args.profileId, updateData);
    return args.profileId;
  },
});

// Mutation: Update last login timestamp
export const updateLastLogin = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return; // Silent fail for unauthenticated users
    }

    const profile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (profile) {
      await ctx.db.patch(profile._id, {
        lastLoginAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
  },
});

// Mutation: Complete onboarding (Enhanced for 4-step flow)
export const completeOnboarding = mutation({
  args: {
    displayName: v.optional(v.string()), // Optional since it might already be set in step 2
    sessionData: v.optional(v.object({
      name: v.string(),
      eventType: v.optional(v.string()),
      duration: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const profile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!profile) {
      throw new Error("DJ profile not found. Please create a profile first.");
    }

    if (profile.completedOnboarding) {
      throw new Error("Onboarding already completed");
    }

    // Validate display name if provided
    if (args.displayName !== undefined) {
      if (args.displayName.trim().length === 0) {
        throw new ValidationError(["Display name is required for onboarding"]);
      }
      if (args.displayName.length > 100) {
        throw new ValidationError(["Display name cannot exceed 100 characters"]);
      }
    }

    // Ensure we have a display name (either from args or existing profile)
    const finalDisplayName = args.displayName?.trim() || profile.displayName;
    if (!finalDisplayName) {
      throw new ValidationError(["Display name is required to complete onboarding"]);
    }

    const updateData: any = {
      completedOnboarding: true,
      onboardingStep: undefined, // Clear onboarding step
      onboardingData: undefined, // Clear temporary onboarding data
      updatedAt: Date.now(),
    };

    // Update display name if provided
    if (args.displayName) {
      updateData.displayName = finalDisplayName;
    }

    await ctx.db.patch(profile._id, updateData);

    // Optionally create first session if session data provided
    let sessionId = null;
    if (args.sessionData) {
      try {
        sessionId = await ctx.db.insert("sessions", {
          djId: profile._id,
          name: args.sessionData.name,
          active: false, // Sessions start inactive
          acceptRequests: true,
          autoApproval: false,
          durationMinutes: args.sessionData.duration || profile.defaultSessionDuration || 240, // 4 hours default
          maxRequestsPerUser: profile.defaultRequestLimit || 5,
          timeframeMinutes: 60,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      } catch (error) {
        console.error("Failed to create first session:", error);
        // Don't fail onboarding completion if session creation fails
      }
    }

    return {
      profileId: profile._id,
      sessionId,
      message: "Onboarding completed successfully"
    };
  },
});

// Mutation: Delete DJ profile (soft delete by marking inactive)
export const deleteDjProfile = mutation({
  args: {
    profileId: v.id("djProfiles"),
    confirmDelete: v.boolean(),
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Delete confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const profile = await ctx.db.get(args.profileId);
    if (!profile) {
      throw new Error("DJ profile not found");
    }

    if (profile.userId !== userId) {
      throw new Error("Access denied: You can only delete your own profile");
    }

    // Check for active sessions before deletion
    const activeSessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj_active", (q) => 
        q.eq("djId", args.profileId).eq("active", true)
      )
      .first();

    if (activeSessions) {
      throw new Error("Cannot delete profile with active sessions. Please end all sessions first.");
    }

    // For now, we'll actually delete the profile
    // In production, you might want to implement soft delete
    await ctx.db.delete(args.profileId);
    
    return { success: true, message: "DJ profile deleted successfully" };
  },
});
