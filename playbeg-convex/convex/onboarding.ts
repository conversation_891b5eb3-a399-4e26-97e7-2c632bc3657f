import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get current onboarding progress
export const getOnboardingProgress = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) return null;

    return {
      step: djProfile.onboardingStep || 1,
      data: djProfile.onboardingData || {},
      completed: djProfile.completedOnboarding,
      profileId: djProfile._id,
    };
  },
});

// Mutation: Save onboarding progress
export const saveOnboardingProgress = mutation({
  args: {
    step: v.number(),
    data: v.any(),
    skipped: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    // Validate step number
    if (args.step < 1 || args.step > 4) {
      throw new Error("Invalid step number. Must be between 1 and 4.");
    }

    await ctx.db.patch(djProfile._id, {
      onboardingStep: args.step,
      onboardingData: {
        ...djProfile.onboardingData,
        ...args.data,
        lastUpdated: Date.now(),
        skipped: args.skipped || false,
      },
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Mutation: Update profile information (Step 2)
export const updateProfileInformation = mutation({
  args: {
    displayName: v.optional(v.string()),
    bio: v.optional(v.string()),
    venueType: v.optional(v.string()),
    yearsExperience: v.optional(v.string()),
    location: v.optional(v.string()),
    profilePictureStorageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    // Validate display name if provided
    if (args.displayName !== undefined) {
      if (args.displayName.trim().length === 0) {
        throw new Error("Display name cannot be empty");
      }
      if (args.displayName.length > 100) {
        throw new Error("Display name cannot exceed 100 characters");
      }
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Only update fields that are provided
    if (args.displayName !== undefined) updateData.displayName = args.displayName.trim();
    if (args.bio !== undefined) updateData.bio = args.bio;
    if (args.venueType !== undefined) updateData.venueType = args.venueType;
    if (args.yearsExperience !== undefined) updateData.yearsExperience = args.yearsExperience;
    if (args.location !== undefined) updateData.location = args.location;
    if (args.profilePictureStorageId !== undefined) updateData.profilePictureStorageId = args.profilePictureStorageId;

    await ctx.db.patch(djProfile._id, updateData);

    return { success: true, profileId: djProfile._id };
  },
});

// Mutation: Update music service connection (Step 3)
export const updateMusicServiceConnection = mutation({
  args: {
    service: v.union(v.literal("apple"), v.literal("spotify"), v.literal("manual")),
    serviceData: v.optional(v.object({
      appleMusicToken: v.optional(v.string()),
      appleMusicLastSync: v.optional(v.number()),
      spotifyAccessToken: v.optional(v.string()),
      spotifyRefreshToken: v.optional(v.string()),
      spotifyLastSync: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    await ctx.db.patch(djProfile._id, {
      connectedMusicService: args.service,
      musicServiceData: args.serviceData || undefined,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Mutation: Update session defaults (Step 4)
export const updateSessionDefaults = mutation({
  args: {
    defaultSessionDuration: v.optional(v.number()),
    defaultRequestLimit: v.optional(v.number()),
    defaultRequestHandling: v.optional(v.union(
      v.literal("ask"), 
      v.literal("auto-accept"), 
      v.literal("auto-deny")
    )),
    defaultGenreBlocks: v.optional(v.array(v.string())),
    defaultNotifications: v.optional(v.union(
      v.literal("immediate"), 
      v.literal("batched"), 
      v.literal("off")
    )),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Only update fields that are provided
    if (args.defaultSessionDuration !== undefined) updateData.defaultSessionDuration = args.defaultSessionDuration;
    if (args.defaultRequestLimit !== undefined) updateData.defaultRequestLimit = args.defaultRequestLimit;
    if (args.defaultRequestHandling !== undefined) updateData.defaultRequestHandling = args.defaultRequestHandling;
    if (args.defaultGenreBlocks !== undefined) updateData.defaultGenreBlocks = args.defaultGenreBlocks;
    if (args.defaultNotifications !== undefined) updateData.defaultNotifications = args.defaultNotifications;

    await ctx.db.patch(djProfile._id, updateData);

    return { success: true };
  },
});

// Mutation: Reset onboarding progress (for testing)
export const resetOnboardingProgress = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Authentication required");

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) throw new Error("DJ profile not found");

    await ctx.db.patch(djProfile._id, {
      completedOnboarding: false,
      onboardingStep: 1,
      onboardingData: {},
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Query: Check if user needs onboarding
export const needsOnboarding = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return true; // Unauthenticated users need to sign up first

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) return true; // No profile means needs onboarding
    
    return !djProfile.completedOnboarding;
  },
});
