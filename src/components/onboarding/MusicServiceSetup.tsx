import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Music, Check, AlertCircle, ExternalLink, Info, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAppleMusicAuth } from '@/hooks/useAppleMusicAuth';
import { cn } from '@/lib/utils';

interface MusicServiceSetupProps {
  onNext: (service: 'apple' | 'spotify' | 'manual') => void;
  onSkip: () => void;
  onBack: () => void;
  initialService?: 'apple' | 'spotify' | 'manual';
  className?: string;
}

type ServiceState = 'idle' | 'connecting' | 'connected' | 'error';

interface ServiceOption {
  id: 'apple' | 'spotify' | 'manual';
  name: string;
  icon: string;
  description: string;
  features: string[];
  limitations?: string[];
  comingSoon?: boolean;
}

const serviceOptions: ServiceOption[] = [
  {
    id: 'apple',
    name: 'Apple Music',
    icon: '🍎',
    description: 'Full integration with your Apple Music library',
    features: [
      'Automatic playlist creation',
      'Song validation & metadata',
      'Seamless library access',
      'High-quality previews'
    ],
  },
  {
    id: 'spotify',
    name: 'Spotify',
    icon: '🎵',
    description: 'Connect with Spotify for song validation',
    features: [
      'Song search & validation',
      'Rich metadata & artwork',
      'Popular track suggestions',
      'Genre detection'
    ],
    limitations: [
      'Manual playlist management',
      'No automatic song adding'
    ],
    comingSoon: true,
  },
  {
    id: 'manual',
    name: 'Manual Entry',
    icon: '✏️',
    description: 'Basic functionality without music service integration',
    features: [
      'Always available',
      'No account required',
      'Full control over requests'
    ],
    limitations: [
      'No automatic validation',
      'Limited metadata',
      'Manual song entry only'
    ],
  },
];

export const MusicServiceSetup: React.FC<MusicServiceSetupProps> = ({
  onNext,
  onSkip,
  onBack,
  initialService,
  className,
}) => {
  const [selectedService, setSelectedService] = useState<'apple' | 'spotify' | 'manual' | null>(
    initialService || null
  );
  const [serviceStates, setServiceStates] = useState<Record<string, ServiceState>>({
    apple: 'idle',
    spotify: 'idle',
    manual: 'idle',
  });
  const [showLearnMore, setShowLearnMore] = useState(false);
  const { toast } = useToast();

  // Apple Music integration
  const { connectionState: appleConnectionState, connectAppleMusic, isAuthorized: appleAuthorized } = useAppleMusicAuth();

  // Update Apple Music state based on connection
  useEffect(() => {
    if (appleConnectionState === 'connected') {
      setServiceStates(prev => ({ ...prev, apple: 'connected' }));
      if (selectedService === 'apple') {
        // Auto-advance if Apple Music was selected and is now connected
        setTimeout(() => onNext('apple'), 1000);
      }
    } else if (appleConnectionState === 'connecting') {
      setServiceStates(prev => ({ ...prev, apple: 'connecting' }));
    } else if (appleConnectionState === 'error') {
      setServiceStates(prev => ({ ...prev, apple: 'error' }));
    } else {
      setServiceStates(prev => ({ ...prev, apple: 'idle' }));
    }
  }, [appleConnectionState, selectedService, onNext]);

  const handleServiceSelect = async (serviceId: 'apple' | 'spotify' | 'manual') => {
    setSelectedService(serviceId);

    if (serviceId === 'apple') {
      if (appleAuthorized) {
        // Already connected, proceed immediately
        onNext('apple');
      } else {
        // Need to connect
        setServiceStates(prev => ({ ...prev, apple: 'connecting' }));
        try {
          const success = await connectAppleMusic();
          if (success) {
            setServiceStates(prev => ({ ...prev, apple: 'connected' }));
            toast({
              title: "Apple Music Connected",
              description: "Successfully connected to Apple Music",
            });
            setTimeout(() => onNext('apple'), 1000);
          } else {
            setServiceStates(prev => ({ ...prev, apple: 'error' }));
          }
        } catch (error) {
          setServiceStates(prev => ({ ...prev, apple: 'error' }));
          toast({
            title: "Connection Failed",
            description: "Failed to connect to Apple Music. You can try again later.",
            variant: "destructive",
          });
        }
      }
    } else if (serviceId === 'spotify') {
      // Spotify OAuth flow would go here
      toast({
        title: "Coming Soon",
        description: "Spotify integration is coming soon! For now, you can use manual entry.",
        variant: "default",
      });
      setSelectedService(null);
    } else if (serviceId === 'manual') {
      setServiceStates(prev => ({ ...prev, manual: 'connected' }));
      setTimeout(() => onNext('manual'), 500);
    }
  };

  const getServiceState = (serviceId: string): ServiceState => {
    if (serviceId === 'apple' && appleAuthorized) {
      return 'connected';
    }
    return serviceStates[serviceId] || 'idle';
  };

  const renderServiceCard = (service: ServiceOption) => {
    const state = getServiceState(service.id);
    const isSelected = selectedService === service.id;
    const isConnected = state === 'connected';
    const isConnecting = state === 'connecting';
    const hasError = state === 'error';

    return (
      <motion.div
        key={service.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "relative cursor-pointer transition-all duration-200",
          service.comingSoon && "opacity-60"
        )}
        onClick={() => !service.comingSoon && handleServiceSelect(service.id)}
      >
        <Card className={cn(
          "bg-black/40 backdrop-blur-xl border-2 transition-all duration-200 hover:border-purple-500/40",
          {
            "border-purple-500 bg-purple-500/10": isSelected && !service.comingSoon,
            "border-green-500 bg-green-500/10": isConnected,
            "border-red-500 bg-red-500/10": hasError,
            "border-purple-500/20": !isSelected && !isConnected && !hasError,
            "cursor-not-allowed": service.comingSoon,
          }
        )}>
          {service.comingSoon && (
            <div className="absolute top-2 right-2 px-2 py-1 bg-orange-500 text-white text-xs font-medium rounded-full">
              Coming Soon
            </div>
          )}

          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <div className="text-3xl">{service.icon}</div>
                <div>
                  <h3 className="text-lg font-semibold">{service.name}</h3>
                  <p className="text-sm text-gray-400 font-normal">
                    {service.description}
                  </p>
                </div>
              </div>
              
              {isConnecting && (
                <Loader2 className="w-5 h-5 text-purple-500 animate-spin" />
              )}
              {isConnected && (
                <Check className="w-5 h-5 text-green-500" />
              )}
              {hasError && (
                <AlertCircle className="w-5 h-5 text-red-500" />
              )}
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Features */}
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">Features:</h4>
              <ul className="space-y-1">
                {service.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-400">
                    <Check className="w-3 h-3 text-green-400 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Limitations */}
            {service.limitations && service.limitations.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-2">Note:</h4>
                <ul className="space-y-1">
                  {service.limitations.slice(0, 2).map((limitation, index) => (
                    <li key={index} className="flex items-center text-sm text-amber-400">
                      <Info className="w-3 h-3 mr-2 flex-shrink-0" />
                      {limitation}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Connection Status */}
            {isConnected && (
              <Alert className="border-green-500/20 bg-green-500/10">
                <Check className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-green-400">
                  Connected and ready to use
                </AlertDescription>
              </Alert>
            )}

            {hasError && (
              <Alert className="border-red-500/20 bg-red-500/10">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <AlertDescription className="text-red-400">
                  Connection failed. You can try again later.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className={cn("max-w-4xl mx-auto p-6", className)}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">
            Choose Your Music Service
          </h2>
          <p className="text-gray-400 mb-4">
            Connect a music service for the best experience, or start with manual entry
          </p>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowLearnMore(!showLearnMore)}
            className="text-purple-400 hover:text-purple-300"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Learn More About Each Option
          </Button>
        </div>

        {/* Learn More Section */}
        {showLearnMore && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-8"
          >
            <Alert className="border-blue-500/20 bg-blue-500/10">
              <Info className="h-4 w-4 text-blue-400" />
              <AlertDescription className="text-blue-300">
                <strong>Apple Music:</strong> Full integration with playlist creation and song management. 
                <br />
                <strong>Spotify:</strong> Song validation and metadata (coming soon). 
                <br />
                <strong>Manual Entry:</strong> Basic functionality without requiring any music service account.
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Service Options */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {serviceOptions.map(renderServiceCard)}
        </div>

        {/* Single Service Notice */}
        <div className="mb-8">
          <Alert className="border-purple-500/20 bg-purple-500/10">
            <Info className="h-4 w-4 text-purple-400" />
            <AlertDescription className="text-purple-300">
              <strong>Note:</strong> You can only connect one music service at a time. 
              You can change this later in your settings.
            </AlertDescription>
          </Alert>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-gray-400 hover:text-white"
          >
            Back
          </Button>
          
          <div className="flex gap-4">
            <Button
              variant="outline"
              onClick={onSkip}
              className="border-purple-500/50 text-purple-400 hover:bg-purple-500/10"
            >
              Skip for Now
            </Button>
            
            {selectedService && (
              <Button
                onClick={() => onNext(selectedService)}
                className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 text-white"
                disabled={serviceStates[selectedService] === 'connecting'}
              >
                {serviceStates[selectedService] === 'connecting' ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  'Continue'
                )}
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default MusicServiceSetup;
