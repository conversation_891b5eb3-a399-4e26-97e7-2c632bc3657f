import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

// Import onboarding step components
import ProgressIndicator from './ProgressIndicator';
import WelcomeScreen from './WelcomeScreen';
import ProfileSetup from './ProfileSetup';
import MusicServiceSetup from './MusicServiceSetup';
import SessionDefaultsAndCreation from './SessionDefaultsAndCreation';

interface OnboardingRouterProps {
  className?: string;
}

type OnboardingStep = 1 | 2 | 3 | 4;

interface OnboardingData {
  // Step 2: Profile data
  displayName?: string;
  bio?: string;
  venueType?: string;
  yearsExperience?: string;
  location?: string;
  profilePictureFile?: File;
  
  // Step 3: Music service data
  connectedMusicService?: 'apple' | 'spotify' | 'manual';
  
  // Step 4: Session defaults
  sessionName?: string;
  eventType?: string;
  duration?: number;
  requestLimit?: number;
  requestHandling?: 'ask' | 'auto-accept' | 'auto-deny';
  notifications?: 'immediate' | 'batched' | 'off';
  genreBlocks?: string[];
  selectedPlan?: string;
}

const stepLabels = ['Welcome', 'Profile', 'Music Service', 'Session Setup'];

export const OnboardingRouter: React.FC<OnboardingRouterProps> = ({ className }) => {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>(1);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({});
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Convex queries and mutations
  const onboardingProgress = useQuery(api.onboarding.getOnboardingProgress, {});
  const saveProgress = useMutation(api.onboarding.saveOnboardingProgress);
  const updateProfile = useMutation(api.onboarding.updateProfileInformation);
  const updateMusicService = useMutation(api.onboarding.updateMusicServiceConnection);
  const updateSessionDefaults = useMutation(api.onboarding.updateSessionDefaults);
  const completeOnboarding = useMutation(api.djProfiles.completeOnboarding);

  // Restore onboarding state on mount
  useEffect(() => {
    if (onboardingProgress !== undefined) {
      if (onboardingProgress) {
        setCurrentStep(onboardingProgress.step as OnboardingStep);
        setOnboardingData(onboardingProgress.data || {});
      }
      setIsLoading(false);
    }
  }, [onboardingProgress]);

  // Save progress whenever data changes
  const saveOnboardingProgress = useCallback(async (step: OnboardingStep, data: OnboardingData, skipped = false) => {
    try {
      await saveProgress({
        step,
        data,
        skipped,
      });
    } catch (error) {
      console.error('Failed to save onboarding progress:', error);
    }
  }, [saveProgress]);

  const handleNext = useCallback(async (stepData: any) => {
    const newData = { ...onboardingData, ...stepData };
    setOnboardingData(newData);

    if (currentStep < 4) {
      const nextStep = (currentStep + 1) as OnboardingStep;
      setCurrentStep(nextStep);
      await saveOnboardingProgress(nextStep, newData);
    } else {
      // Complete onboarding
      await handleComplete(newData);
    }
  }, [currentStep, onboardingData, saveOnboardingProgress]);

  const handleSkip = useCallback(async (stepData: Partial<OnboardingData> = {}) => {
    const newData = { ...onboardingData, ...stepData };
    setOnboardingData(newData);

    if (currentStep < 4) {
      const nextStep = (currentStep + 1) as OnboardingStep;
      setCurrentStep(nextStep);
      await saveOnboardingProgress(nextStep, newData, true);
    } else {
      // Complete onboarding with defaults
      await handleComplete(newData);
    }
  }, [currentStep, onboardingData, saveOnboardingProgress]);

  const handleBack = useCallback(async () => {
    if (currentStep > 1) {
      const prevStep = (currentStep - 1) as OnboardingStep;
      setCurrentStep(prevStep);
      await saveOnboardingProgress(prevStep, onboardingData);
    }
  }, [currentStep, onboardingData, saveOnboardingProgress]);

  const handleComplete = async (finalData: OnboardingData) => {
    try {
      setIsLoading(true);

      // Update profile information
      if (finalData.displayName || finalData.bio || finalData.venueType) {
        await updateProfile({
          displayName: finalData.displayName,
          bio: finalData.bio,
          venueType: finalData.venueType,
          yearsExperience: finalData.yearsExperience,
          location: finalData.location,
          // Note: File upload would need to be handled separately
        });
      }

      // Update music service connection
      if (finalData.connectedMusicService) {
        await updateMusicService({
          service: finalData.connectedMusicService,
          serviceData: {
            // Service-specific data is handled by the respective auth hooks
            // Apple Music: handled by useAppleMusicAuth
            // Spotify: handled by useSpotifyAuth
            // Manual: no additional data needed
            lastSync: Date.now(),
          },
        });
      }

      // Update session defaults
      if (finalData.duration || finalData.requestLimit) {
        await updateSessionDefaults({
          defaultSessionDuration: finalData.duration,
          defaultRequestLimit: finalData.requestLimit,
          defaultRequestHandling: finalData.requestHandling,
          defaultGenreBlocks: finalData.genreBlocks,
          defaultNotifications: finalData.notifications,
        });
      }

      // Complete onboarding
      await completeOnboarding({
        displayName: finalData.displayName,
        sessionData: finalData.sessionName ? {
          name: finalData.sessionName,
          eventType: finalData.eventType,
          duration: finalData.duration,
        } : undefined,
      });

      toast({
        title: "Welcome to PlayBeg!",
        description: "Your onboarding is complete. Let's start creating amazing sessions!",
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
      toast({
        title: "Error",
        description: "Failed to complete onboarding. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-400">Please sign in to continue with onboarding.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
          className="h-10 w-10 border-4 border-t-purple-500 border-r-cyan-500 border-b-purple-500/30 border-l-cyan-500/30 rounded-full"
        />
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900 ${className}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Progress Indicator */}
        <div className="mb-8">
          <ProgressIndicator
            currentStep={currentStep}
            totalSteps={4}
            stepLabels={stepLabels}
          />
        </div>

        {/* Step Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {currentStep === 1 && (
              <WelcomeScreen
                onNext={() => handleNext({})}
                onSkip={() => handleSkip()}
                autoAdvanceDelay={0} // Disable auto-advance
              />
            )}

            {currentStep === 2 && (
              <ProfileSetup
                onNext={(data) => handleNext(data)}
                onSkip={(data) => handleSkip(data)}
                onBack={handleBack}
                initialData={onboardingData}
              />
            )}

            {currentStep === 3 && (
              <MusicServiceSetup
                onNext={(service) => handleNext({ connectedMusicService: service })}
                onSkip={() => handleSkip({ connectedMusicService: 'manual' })}
                onBack={handleBack}
                initialService={onboardingData.connectedMusicService}
              />
            )}

            {currentStep === 4 && (
              <SessionDefaultsAndCreation
                onNext={(data) => handleNext(data)}
                onSkip={(data) => handleSkip(data)}
                onBack={handleBack}
                connectedService={onboardingData.connectedMusicService || 'manual'}
                initialData={onboardingData}
              />
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default OnboardingRouter;
