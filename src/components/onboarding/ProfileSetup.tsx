import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { User, MapPin, Calendar, Building, Upload, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Form validation schema
const profileSchema = z.object({
  displayName: z.string().min(1, 'DJ name is required').max(100, 'Name too long'),
  venueType: z.string().min(1, 'Venue type is required'),
  bio: z.string().max(500, 'Bio too long').optional(),
  yearsExperience: z.string().optional(),
  location: z.string().max(100, 'Location too long').optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface ProfileSetupProps {
  onNext: (data: ProfileFormData & { profilePictureFile?: File }) => void;
  onSkip: (data: Partial<ProfileFormData>) => void;
  onBack: () => void;
  initialData?: Partial<ProfileFormData>;
  className?: string;
}

const venueTypes = [
  'Club/Nightclub',
  'Wedding Venue',
  'Corporate Events',
  'Private Parties',
  'Bars/Restaurants',
  'Festivals/Outdoor',
  'Mobile DJ',
  'Radio/Streaming',
  'Other',
];

const experienceOptions = [
  'Just starting out',
  '1-2 years',
  '3-5 years',
  '6-10 years',
  '10+ years',
  'Professional (15+ years)',
];

export const ProfileSetup: React.FC<ProfileSetupProps> = ({
  onNext,
  onSkip,
  onBack,
  initialData,
  className,
}) => {
  const [profilePicture, setProfilePicture] = useState<string | null>(null);
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');
  const { toast } = useToast();

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      displayName: initialData?.displayName || '',
      venueType: initialData?.venueType || '',
      bio: initialData?.bio || '',
      yearsExperience: initialData?.yearsExperience || '',
      location: initialData?.location || '',
    },
  });

  // Auto-save functionality
  const autoSave = useCallback(async (data: Partial<ProfileFormData>) => {
    setAutoSaveStatus('saving');
    // Simulate API call - replace with actual onboarding progress save
    await new Promise(resolve => setTimeout(resolve, 500));
    setAutoSaveStatus('saved');
    setTimeout(() => setAutoSaveStatus('idle'), 2000);
  }, []);

  // Auto-save every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      const currentData = form.getValues();
      if (Object.values(currentData).some(value => value && value.length > 0)) {
        autoSave(currentData);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [form, autoSave]);

  const handleProfilePictureChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image under 5MB",
        variant: "destructive"
      });
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);
    try {
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setProfilePicture(previewUrl);
      setProfilePictureFile(file);
      
      toast({
        title: "Profile picture selected",
        description: "Your profile picture will be uploaded when you continue",
      });
    } catch (error) {
      toast({
        title: "Error processing image",
        description: "Please try selecting a different image",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const onSubmit = (data: ProfileFormData) => {
    onNext({ ...data, profilePictureFile: profilePictureFile || undefined });
  };

  const handleSkip = () => {
    const currentData = form.getValues();
    onSkip(currentData);
  };

  return (
    <div className={cn("max-w-2xl mx-auto p-6", className)}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">
            Set Up Your DJ Profile
          </h2>
          <p className="text-gray-400">
            Tell us about yourself so we can personalize your experience
          </p>
        </div>

        <Card className="bg-black/40 backdrop-blur-xl border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center justify-between">
              Profile Information
              {autoSaveStatus === 'saving' && (
                <span className="text-sm text-gray-400 flex items-center">
                  <Save className="w-4 h-4 mr-1 animate-spin" />
                  Saving...
                </span>
              )}
              {autoSaveStatus === 'saved' && (
                <span className="text-sm text-green-400 flex items-center">
                  <Save className="w-4 h-4 mr-1" />
                  Saved
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Profile Picture Upload */}
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <Avatar className="h-24 w-24 border-2 border-purple-500/30">
                  {profilePicture ? (
                    <AvatarImage src={profilePicture} alt="Profile preview" />
                  ) : (
                    <AvatarFallback className="bg-gradient-to-br from-purple-500/20 to-cyan-500/20 text-white text-xl">
                      {form.watch('displayName')?.charAt(0)?.toUpperCase() || 'DJ'}
                    </AvatarFallback>
                  )}
                </Avatar>
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="absolute -bottom-2 -right-2 border-purple-500/50 text-purple-400 bg-black/80 hover:bg-black/60"
                  disabled={isUploading}
                >
                  {isUploading ? (
                    <>Uploading...</>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                    </>
                  )}
                  <input
                    type="file"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    accept="image/*"
                    onChange={handleProfilePictureChange}
                    disabled={isUploading}
                  />
                </Button>
              </div>
              <p className="text-sm text-gray-400 text-center">
                Upload a profile picture (optional)
              </p>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Required Fields */}
                <div className="grid md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="displayName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-white">
                          <User className="h-4 w-4 text-purple-400" />
                          DJ Name *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Your DJ name"
                            {...field}
                            className="bg-black/30 border-purple-500/30 focus-visible:ring-purple-500/50 text-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="venueType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-white">
                          <Building className="h-4 w-4 text-purple-400" />
                          Venue Type *
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="bg-black/30 border-purple-500/30 text-white">
                              <SelectValue placeholder="Select venue type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {venueTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Optional Fields */}
                <div className="grid md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="yearsExperience"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-white">
                          <Calendar className="h-4 w-4 text-purple-400" />
                          Experience Level
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="bg-black/30 border-purple-500/30 text-white">
                              <SelectValue placeholder="Select experience" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {experienceOptions.map((option) => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-white">
                          <MapPin className="h-4 w-4 text-purple-400" />
                          Location
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="City, State/Country"
                            {...field}
                            className="bg-black/30 border-purple-500/30 focus-visible:ring-purple-500/50 text-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="bio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">
                        Bio (Optional)
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Tell us about your DJ style, favorite genres, or anything else you'd like to share..."
                          {...field}
                          className="bg-black/30 border-purple-500/30 focus-visible:ring-purple-500/50 text-white min-h-[100px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onBack}
                    className="text-gray-400 hover:text-white"
                  >
                    Back
                  </Button>
                  
                  <div className="flex gap-4 sm:ml-auto">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSkip}
                      className="border-purple-500/50 text-purple-400 hover:bg-purple-500/10"
                    >
                      Skip for Now
                    </Button>
                    
                    <Button
                      type="submit"
                      className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 text-white"
                    >
                      Continue
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default ProfileSetup;
