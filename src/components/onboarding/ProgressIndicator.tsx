import React from 'react';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  stepLabels: string[];
  className?: string;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
  stepLabels,
  className,
}) => {
  const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* Progress Bar */}
      <div className="relative">
        <Progress 
          value={progressPercentage} 
          className="h-2 bg-gray-700"
          indicatorClassName="bg-gradient-to-r from-purple-500 to-cyan-400"
        />
        
        {/* Step Indicators */}
        <div className="absolute top-0 left-0 w-full h-2 flex justify-between items-center">
          {Array.from({ length: totalSteps }, (_, index) => {
            const stepNumber = index + 1;
            const isCompleted = stepNumber < currentStep;
            const isCurrent = stepNumber === currentStep;
            
            return (
              <div
                key={stepNumber}
                className={cn(
                  "w-4 h-4 rounded-full border-2 flex items-center justify-center text-xs font-medium transition-all duration-200 -mt-1",
                  {
                    "bg-gradient-to-r from-purple-500 to-cyan-400 border-transparent text-white": isCompleted,
                    "bg-white border-purple-500 text-purple-600": isCurrent,
                    "bg-gray-600 border-gray-500 text-gray-400": !isCompleted && !isCurrent,
                  }
                )}
              >
                {isCompleted ? (
                  <svg className="w-2.5 h-2.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  stepNumber
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Labels */}
      <div className="flex justify-between items-center">
        {stepLabels.map((label, index) => {
          const stepNumber = index + 1;
          const isCompleted = stepNumber < currentStep;
          const isCurrent = stepNumber === currentStep;
          
          return (
            <div
              key={stepNumber}
              className={cn(
                "text-center transition-all duration-200",
                "flex-1 px-1" // Ensure equal spacing
              )}
            >
              <div
                className={cn(
                  "text-sm font-medium transition-colors duration-200",
                  {
                    "text-purple-400": isCompleted,
                    "text-white": isCurrent,
                    "text-gray-500": !isCompleted && !isCurrent,
                  }
                )}
              >
                {label}
              </div>
              
              {/* Mobile: Show step number below label */}
              <div className="md:hidden mt-1">
                <span
                  className={cn(
                    "text-xs",
                    {
                      "text-purple-300": isCompleted,
                      "text-gray-300": isCurrent,
                      "text-gray-600": !isCompleted && !isCurrent,
                    }
                  )}
                >
                  Step {stepNumber}
                </span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Mobile: Current Step Info */}
      <div className="md:hidden text-center">
        <div className="text-gray-400 text-sm">
          Step {currentStep} of {totalSteps}
        </div>
        <div className="text-white font-medium">
          {stepLabels[currentStep - 1]}
        </div>
      </div>
    </div>
  );
};

export default ProgressIndicator;
