import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Settings, 
  Clock, 
  Users, 
  Shield, 
  Bell, 
  ChevronDown, 
  ChevronUp,
  AlertTriangle,
  Info,
  CheckCircle,
  CreditCard,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useToast } from '@/hooks/use-toast';
import { useSubscription } from '@/hooks/useSubscription';
import { cn } from '@/lib/utils';

// Form validation schema
const sessionSchema = z.object({
  sessionName: z.string().min(1, 'Session name is required').max(100, 'Name too long'),
  eventType: z.string().min(1, 'Event type is required'),
  duration: z.number().min(1, 'Duration must be at least 1 minute'),
  requestLimit: z.number().min(1, 'Request limit must be at least 1'),
  requestHandling: z.enum(['ask', 'auto-accept', 'auto-deny']),
  notifications: z.enum(['immediate', 'batched', 'off']),
  genreBlocks: z.array(z.string()).optional(),
});

type SessionFormData = z.infer<typeof sessionSchema>;

interface SessionDefaultsAndCreationProps {
  onNext: (data: SessionFormData & { selectedPlan?: string }) => void;
  onSkip: (data: Partial<SessionFormData>) => void;
  onBack: () => void;
  connectedService: 'apple' | 'spotify' | 'manual';
  initialData?: Partial<SessionFormData>;
  className?: string;
}

const eventTypes = [
  'Practice Session',
  'Wedding',
  'Corporate Event',
  'Private Party',
  'Club Night',
  'Festival',
  'Bar/Restaurant',
  'Other',
];

const genreOptions = [
  'Explicit Content',
  'Heavy Metal',
  'Country',
  'Classical',
  'Jazz',
  'Reggae',
  'Hip Hop',
  'Electronic',
];

// Mock subscription plans - replace with actual data
const subscriptionPlans = [
  {
    id: 'free',
    name: 'Free',
    price: '$0',
    duration: '20min',
    requests: '3 req',
    limits: { maxDuration: 20, maxRequests: 3 },
    description: 'Perfect for trying out PlayBeg',
  },
  {
    id: '24hour',
    name: '24-Hour',
    price: '$9.99',
    duration: '24h',
    requests: '100 req',
    limits: { maxDuration: 1440, maxRequests: 100 },
    description: 'Single event coverage',
    popular: false,
  },
  {
    id: '48hour',
    name: '48-Hour',
    price: '$17.99',
    duration: '48h',
    requests: '100 req',
    limits: { maxDuration: 2880, maxRequests: 100 },
    description: 'Weekend events',
    popular: true,
  },
  {
    id: '7day',
    name: '7-Day',
    price: '$49.99',
    duration: '7d',
    requests: '100 req',
    limits: { maxDuration: 10080, maxRequests: 100 },
    description: 'Multiple events',
    popular: false,
  },
];

export const SessionDefaultsAndCreation: React.FC<SessionDefaultsAndCreationProps> = ({
  onNext,
  onSkip,
  onBack,
  connectedService,
  initialData,
  className,
}) => {
  const [selectedPlan, setSelectedPlan] = useState('free');
  const [bannerExpanded, setBannerExpanded] = useState(false);
  const [advancedExpanded, setAdvancedExpanded] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const { toast } = useToast();
  
  // Mock subscription state - replace with actual hook
  const subscriptionState = {
    plan: 'free' as const,
    status: 'free' as const, // 'paid' | 'grace' | 'free'
    expiresAt: undefined,
    graceEndsAt: undefined,
  };

  const form = useForm<SessionFormData>({
    resolver: zodResolver(sessionSchema),
    defaultValues: {
      sessionName: initialData?.sessionName || 'My First PlayBeg Session',
      eventType: initialData?.eventType || '',
      duration: initialData?.duration || 240, // 4 hours default
      requestLimit: initialData?.requestLimit || 5,
      requestHandling: initialData?.requestHandling || 'ask',
      notifications: initialData?.notifications || 'immediate',
      genreBlocks: initialData?.genreBlocks || [],
    },
  });

  const currentPlan = subscriptionPlans.find(p => p.id === selectedPlan) || subscriptionPlans[0];

  // Auto-clamp form values when plan changes
  useEffect(() => {
    const currentDuration = form.getValues('duration');
    const currentRequests = form.getValues('requestLimit');
    
    if (currentDuration > currentPlan.limits.maxDuration) {
      form.setValue('duration', currentPlan.limits.maxDuration);
    }
    if (currentRequests > currentPlan.limits.maxRequests) {
      form.setValue('requestLimit', currentPlan.limits.maxRequests);
    }
  }, [selectedPlan, currentPlan, form]);

  const getBannerContent = () => {
    switch (subscriptionState.status) {
      case 'paid':
        return {
          icon: <CheckCircle className="w-5 h-5 text-green-500" />,
          message: `Your ${subscriptionState.plan} Pass expires in 4h 23m`,
          variant: 'success' as const,
        };
      case 'grace':
        return {
          icon: <AlertTriangle className="w-5 h-5 text-yellow-500" />,
          message: 'Pass expired 2m ago—5m grace left. Upgrade now.',
          variant: 'warning' as const,
        };
      case 'free':
        return {
          icon: <Info className="w-5 h-5 text-blue-500" />,
          message: 'You\'re on Free (20 min/3 req). Upgrade for more time & requests.',
          variant: 'info' as const,
        };
    }
  };

  const banner = getBannerContent();

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleCheckout = () => {
    if (selectedPlan === 'free') return;
    
    toast({
      title: "Checkout",
      description: `Proceeding to checkout for ${currentPlan.name} plan`,
    });
    // Implement actual checkout logic here
  };

  const onSubmit = async (data: SessionFormData) => {
    setIsCreating(true);
    try {
      // Simulate session creation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onNext({ ...data, selectedPlan });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create session. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleSkip = () => {
    const currentData = form.getValues();
    onSkip({ ...currentData, selectedPlan });
  };

  const serviceDisplay = {
    apple: { name: 'Apple Music', icon: '🍎', description: 'Full playlist integration' },
    spotify: { name: 'Spotify', icon: '🎵', description: 'Song search & validation' },
    manual: { name: 'Manual Entry', icon: '✏️', description: 'Basic functionality' },
  };

  const currentService = serviceDisplay[connectedService];

  return (
    <div className={cn("max-w-3xl mx-auto p-6", className)}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">
            Create Your First Session
          </h2>
          <p className="text-gray-400">
            Set your preferences and create your first song request session
          </p>
        </div>

        {/* Subscription Banner */}
        <div className={cn(
          "p-4 rounded-lg border-2 transition-all duration-200 mb-6",
          {
            "bg-green-50/10 border-green-500/20": banner.variant === 'success',
            "bg-yellow-50/10 border-yellow-500/20": banner.variant === 'warning',
            "bg-blue-50/10 border-blue-500/20": banner.variant === 'info',
          }
        )}>
          <div 
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setBannerExpanded(!bannerExpanded)}
          >
            <div className="flex items-center space-x-3">
              {banner.icon}
              <span className="font-medium text-white">{banner.message}</span>
            </div>
            {bannerExpanded ? (
              <ChevronUp className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-400" />
            )}
          </div>
          
          {bannerExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 space-y-4"
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {subscriptionPlans.map((plan) => (
                  <div
                    key={plan.id}
                    className={cn(
                      "relative p-3 rounded-lg border-2 cursor-pointer transition-all",
                      {
                        "border-purple-500 bg-purple-500/10": selectedPlan === plan.id,
                        "border-gray-600 bg-gray-800/50 hover:border-gray-500": selectedPlan !== plan.id,
                      }
                    )}
                    onClick={() => handlePlanSelect(plan.id)}
                  >
                    {plan.popular && (
                      <div className="absolute -top-2 -right-2 px-2 py-1 bg-green-500 text-white text-xs font-medium rounded-full">
                        Popular
                      </div>
                    )}
                    <div className="text-center">
                      <div className="font-medium text-white">{plan.name}</div>
                      <div className="text-sm text-gray-400">{plan.price}</div>
                      <div className="text-xs text-gray-500">{plan.duration}/{plan.requests}</div>
                    </div>
                  </div>
                ))}
              </div>
              
              {selectedPlan !== 'free' && (
                <Button 
                  className="w-full bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600"
                  onClick={handleCheckout}
                >
                  <CreditCard className="w-4 h-4 mr-2" />
                  Proceed to Checkout
                </Button>
              )}
            </motion.div>
          )}
        </div>

        <Card className="bg-black/40 backdrop-blur-xl border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Session Setup</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Music Service Display */}
                <div className="p-4 bg-gray-800/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{currentService.icon}</div>
                    <div>
                      <h4 className="font-medium text-white">
                        Music Service: {currentService.name}
                      </h4>
                      <p className="text-sm text-gray-400">
                        {currentService.description}
                      </p>
                    </div>
                    {connectedService !== 'manual' && (
                      <div className="ml-auto">
                        <Badge className="bg-green-500/10 text-green-400 border-green-500/20">
                          Connected
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>

                {/* Essential Fields */}
                <div className="grid md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="sessionName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Session Name *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            className="bg-black/30 border-purple-500/30 focus-visible:ring-purple-500/50 text-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="eventType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Event Type *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="bg-black/30 border-purple-500/30 text-white">
                              <SelectValue placeholder="Select event type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {eventTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="duration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-white">
                          <Clock className="h-4 w-4 text-purple-400" />
                          Duration (minutes) - Max {currentPlan.limits.maxDuration} for {currentPlan.name}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={(e) => field.onChange(Math.min(parseInt(e.target.value) || 0, currentPlan.limits.maxDuration))}
                            max={currentPlan.limits.maxDuration}
                            className="bg-black/30 border-purple-500/30 focus-visible:ring-purple-500/50 text-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="requestLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2 text-white">
                          <Users className="h-4 w-4 text-purple-400" />
                          Request Limit - Max {currentPlan.limits.maxRequests} for {currentPlan.name}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={(e) => field.onChange(Math.min(parseInt(e.target.value) || 0, currentPlan.limits.maxRequests))}
                            max={currentPlan.limits.maxRequests}
                            className="bg-black/30 border-purple-500/30 focus-visible:ring-purple-500/50 text-white"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Advanced Settings */}
                <Collapsible open={advancedExpanded} onOpenChange={setAdvancedExpanded}>
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="flex items-center gap-2 text-gray-400 hover:text-white p-0"
                    >
                      <Settings className="h-4 w-4" />
                      Advanced Settings (Optional)
                      {advancedExpanded ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-4 mt-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="requestHandling"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2 text-white">
                              <Shield className="h-4 w-4 text-purple-400" />
                              Request Handling
                            </FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="bg-black/30 border-purple-500/30 text-white">
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="ask">Ask each time</SelectItem>
                                <SelectItem value="auto-accept">Auto-accept</SelectItem>
                                <SelectItem value="auto-deny">Auto-deny</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="notifications"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="flex items-center gap-2 text-white">
                              <Bell className="h-4 w-4 text-purple-400" />
                              Notifications
                            </FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="bg-black/30 border-purple-500/30 text-white">
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="immediate">Immediate</SelectItem>
                                <SelectItem value="batched">Batched</SelectItem>
                                <SelectItem value="off">Off</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onBack}
                    className="text-gray-400 hover:text-white"
                  >
                    Back
                  </Button>
                  
                  <div className="flex gap-4 sm:ml-auto">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSkip}
                      className="border-purple-500/50 text-purple-400 hover:bg-purple-500/10"
                    >
                      Skip for Now
                    </Button>
                    
                    <Button
                      type="submit"
                      className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 text-white"
                      disabled={isCreating}
                    >
                      {isCreating ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        'Create Session & Finish'
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default SessionDefaultsAndCreation;
