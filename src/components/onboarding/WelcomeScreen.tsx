import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Music, Users, Zap, ArrowRight, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface WelcomeScreenProps {
  onNext: () => void;
  onSkip: () => void;
  autoAdvanceDelay?: number; // in seconds, 0 to disable
  className?: string;
}

const features = [
  {
    icon: Music,
    title: 'Apple Music Integration',
    description: 'Connect your Apple Music account for seamless song search and playlist management',
    color: 'from-pink-500 to-rose-400',
  },
  {
    icon: Users,
    title: 'Real-Time Requests',
    description: 'Guests can request songs instantly through QR codes - no app downloads required',
    color: 'from-purple-500 to-indigo-400',
  },
  {
    icon: Zap,
    title: 'Instant Setup',
    description: 'Get your first session running in under 5 minutes with our guided setup',
    color: 'from-cyan-500 to-blue-400',
  },
];

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onNext,
  onSkip,
  autoAdvanceDelay = 0,
  className,
}) => {
  const [countdown, setCountdown] = useState(autoAdvanceDelay);
  const [showCountdown, setShowCountdown] = useState(autoAdvanceDelay > 0);

  // Auto-advance countdown
  useEffect(() => {
    if (autoAdvanceDelay > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            onNext();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [autoAdvanceDelay, onNext]);

  const handleGetStarted = () => {
    setShowCountdown(false);
    onNext();
  };

  const handleSkip = () => {
    setShowCountdown(false);
    onSkip();
  };

  return (
    <div className={cn("min-h-screen flex items-center justify-center p-4", className)}>
      <div className="max-w-4xl mx-auto text-center">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12"
        >
          {/* Logo/Brand */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-purple-600 to-cyan-500 mb-6">
              <Music className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
              Welcome to{' '}
              <span className="bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                PlayBeg
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              The modern DJ platform that transforms how you handle song requests. 
              Let's get you set up in just a few simple steps.
            </p>
          </div>
        </motion.div>

        {/* Feature Cards */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid md:grid-cols-3 gap-6 mb-12"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              className="bg-black/40 backdrop-blur-xl rounded-2xl p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300"
            >
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} mb-4`}>
                <feature.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* What You'll Set Up */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-black/30 backdrop-blur-xl rounded-2xl p-8 border border-purple-500/20 mb-12"
        >
          <h2 className="text-2xl font-bold text-white mb-6">
            What we'll set up together:
          </h2>
          <div className="grid md:grid-cols-2 gap-4 text-left">
            {[
              'Your DJ profile and preferences',
              'Music service connection (Apple Music or Spotify)',
              'Session defaults and settings',
              'Your first song request session',
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + index * 0.1 }}
                className="flex items-center text-gray-300"
              >
                <CheckCircle className="h-5 w-5 text-green-400 mr-3 flex-shrink-0" />
                {item}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <Button
            onClick={handleGetStarted}
            size="lg"
            className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 text-white px-8 py-3 text-lg font-medium transition-all duration-300 transform hover:scale-105"
          >
            Get Started
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>

          <Button
            onClick={handleSkip}
            variant="ghost"
            size="lg"
            className="text-gray-400 hover:text-white px-8 py-3 text-lg"
          >
            Skip Setup (Use Defaults)
          </Button>
        </motion.div>

        {/* Auto-advance countdown */}
        {showCountdown && countdown > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-8 flex items-center justify-center text-gray-400"
          >
            <Clock className="w-4 h-4 mr-2" />
            <span className="text-sm">
              Auto-advancing in {countdown} second{countdown !== 1 ? 's' : ''}
            </span>
          </motion.div>
        )}

        {/* Progress hint */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="mt-8 text-gray-500 text-sm"
        >
          This should take about 3-5 minutes to complete
        </motion.div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
