/**
 * Integration tests for the PlayBeg DJ Onboarding Flow
 * 
 * This test file validates the core functionality of the 4-step onboarding process:
 * 1. Welcome Screen
 * 2. Profile Setup
 * 3. Music Service Setup
 * 4. Session Defaults & Creation
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ConvexProvider } from 'convex/react';
import { ConvexReactClient } from 'convex/react';

// Mock components for testing
import ProgressIndicator from '../ProgressIndicator';
import WelcomeScreen from '../WelcomeScreen';
import ProfileSetup from '../ProfileSetup';
import MusicServiceSetup from '../MusicServiceSetup';
import SessionDefaultsAndCreation from '../SessionDefaultsAndCreation';

// Mock Convex client
const mockConvexClient = new ConvexReactClient('mock-url');

// Mock hooks
vi.mock('@/hooks/useAppleMusicAuth', () => ({
  useAppleMusicAuth: () => ({
    isAuthorized: false,
    connectionState: 'idle',
    error: null,
    connectAppleMusic: vi.fn().mockResolvedValue(true),
    syncConnectionState: vi.fn(),
  }),
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

vi.mock('@/context/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id', email: '<EMAIL>' },
  }),
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConvexProvider client={mockConvexClient}>
    <div className="min-h-screen bg-gray-900">
      {children}
    </div>
  </ConvexProvider>
);

describe('Onboarding Flow Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ProgressIndicator', () => {
    it('renders all 4 steps with correct labels', () => {
      const stepLabels = ['Welcome', 'Profile', 'Music Service', 'Session Setup'];
      
      render(
        <TestWrapper>
          <ProgressIndicator
            currentStep={1}
            totalSteps={4}
            stepLabels={stepLabels}
          />
        </TestWrapper>
      );

      stepLabels.forEach(label => {
        expect(screen.getByText(label)).toBeInTheDocument();
      });
    });

    it('highlights current step correctly', () => {
      render(
        <TestWrapper>
          <ProgressIndicator
            currentStep={2}
            totalSteps={4}
            stepLabels={['Welcome', 'Profile', 'Music Service', 'Session Setup']}
          />
        </TestWrapper>
      );

      // Current step should be highlighted
      const profileStep = screen.getByText('Profile');
      expect(profileStep).toHaveClass('text-white');
    });

    it('shows completed steps with checkmarks', () => {
      render(
        <TestWrapper>
          <ProgressIndicator
            currentStep={3}
            totalSteps={4}
            stepLabels={['Welcome', 'Profile', 'Music Service', 'Session Setup']}
          />
        </TestWrapper>
      );

      // Should have checkmarks for completed steps (steps 1 and 2)
      const checkmarks = screen.getAllByRole('img', { hidden: true });
      expect(checkmarks.length).toBeGreaterThan(0);
    });
  });

  describe('WelcomeScreen', () => {
    it('renders welcome content and action buttons', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();

      render(
        <TestWrapper>
          <WelcomeScreen
            onNext={mockOnNext}
            onSkip={mockOnSkip}
          />
        </TestWrapper>
      );

      expect(screen.getByText(/Welcome to/)).toBeInTheDocument();
      expect(screen.getByText(/PlayBeg/)).toBeInTheDocument();
      expect(screen.getByText('Get Started')).toBeInTheDocument();
      expect(screen.getByText(/Skip Setup/)).toBeInTheDocument();
    });

    it('calls onNext when Get Started is clicked', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();

      render(
        <TestWrapper>
          <WelcomeScreen
            onNext={mockOnNext}
            onSkip={mockOnSkip}
          />
        </TestWrapper>
      );

      fireEvent.click(screen.getByText('Get Started'));
      expect(mockOnNext).toHaveBeenCalledTimes(1);
    });

    it('calls onSkip when Skip button is clicked', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();

      render(
        <TestWrapper>
          <WelcomeScreen
            onNext={mockOnNext}
            onSkip={mockOnSkip}
          />
        </TestWrapper>
      );

      fireEvent.click(screen.getByText(/Skip Setup/));
      expect(mockOnSkip).toHaveBeenCalledTimes(1);
    });
  });

  describe('ProfileSetup', () => {
    it('renders profile form with required fields', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <ProfileSetup
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Set Up Your DJ Profile')).toBeInTheDocument();
      expect(screen.getByLabelText(/DJ Name/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Venue Type/)).toBeInTheDocument();
    });

    it('validates required fields before submission', async () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <ProfileSetup
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
          />
        </TestWrapper>
      );

      // Try to submit without filling required fields
      fireEvent.click(screen.getByText('Continue'));

      await waitFor(() => {
        expect(screen.getByText('DJ name is required')).toBeInTheDocument();
      });

      expect(mockOnNext).not.toHaveBeenCalled();
    });

    it('allows skipping with partial data', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <ProfileSetup
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
          />
        </TestWrapper>
      );

      fireEvent.click(screen.getByText('Skip for Now'));
      expect(mockOnSkip).toHaveBeenCalledTimes(1);
    });
  });

  describe('MusicServiceSetup', () => {
    it('renders music service options', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <MusicServiceSetup
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Choose Your Music Service')).toBeInTheDocument();
      expect(screen.getByText('Apple Music')).toBeInTheDocument();
      expect(screen.getByText('Spotify')).toBeInTheDocument();
      expect(screen.getByText('Manual Entry')).toBeInTheDocument();
    });

    it('shows single service enforcement notice', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <MusicServiceSetup
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
          />
        </TestWrapper>
      );

      expect(screen.getByText(/You can only connect one music service at a time/)).toBeInTheDocument();
    });

    it('handles manual entry selection', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <MusicServiceSetup
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
          />
        </TestWrapper>
      );

      fireEvent.click(screen.getByText('Manual Entry'));
      
      // Manual entry should proceed immediately
      waitFor(() => {
        expect(mockOnNext).toHaveBeenCalledWith('manual');
      });
    });
  });

  describe('SessionDefaultsAndCreation', () => {
    it('renders session creation form', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <SessionDefaultsAndCreation
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
            connectedService="manual"
          />
        </TestWrapper>
      );

      expect(screen.getByText('Create Your First Session')).toBeInTheDocument();
      expect(screen.getByLabelText(/Session Name/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Event Type/)).toBeInTheDocument();
    });

    it('shows subscription banner', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <SessionDefaultsAndCreation
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
            connectedService="manual"
          />
        </TestWrapper>
      );

      expect(screen.getByText(/You're on Free/)).toBeInTheDocument();
    });

    it('displays connected music service', () => {
      const mockOnNext = vi.fn();
      const mockOnSkip = vi.fn();
      const mockOnBack = vi.fn();

      render(
        <TestWrapper>
          <SessionDefaultsAndCreation
            onNext={mockOnNext}
            onSkip={mockOnSkip}
            onBack={mockOnBack}
            connectedService="apple"
          />
        </TestWrapper>
      );

      expect(screen.getByText(/Music Service: Apple Music/)).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    it('maintains state preservation across components', () => {
      // This would test that data entered in one step is preserved when navigating
      // In a real implementation, this would involve testing the OnboardingRouter
      expect(true).toBe(true); // Placeholder for integration test
    });

    it('handles error states gracefully', () => {
      // Test error handling across the flow
      expect(true).toBe(true); // Placeholder for error handling test
    });

    it('supports mobile responsive design', () => {
      // Test mobile breakpoints and touch interactions
      expect(true).toBe(true); // Placeholder for mobile test
    });
  });
});

export default {};
