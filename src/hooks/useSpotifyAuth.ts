import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { debounce } from 'lodash';

export type SpotifyConnectionState =
  | 'checking'
  | 'connected'
  | 'disconnected'
  | 'expired'
  | 'error'
  | 'connecting';

export interface SpotifyProfile {
  id: string;
  displayName?: string;
  email?: string;
}

export function useSpotifyAuth() {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [connectionState, setConnectionState] = useState<SpotifyConnectionState>('disconnected');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [profile, setProfile] = useState<SpotifyProfile | null>(null);
  
  const initializationAttempts = useRef(0);
  const maxInitializationAttempts = 3;
  const lastRefreshTime = useRef(0);
  const REFRESH_COOLDOWN = 5000; // 5 seconds cooldown
  const hasRunSyncRef = useRef(false);

  // Convex queries and mutations
  const connectionStatus = useQuery(api.spotify_auth.getSpotifyConnectionStatus, {});
  const initiateAuth = useMutation(api.spotify_auth.initiateSpotifyAuth);
  const disconnectSpotify = useMutation(api.spotify_auth.disconnectSpotify);
  const refreshToken = useMutation(api.spotify_auth.refreshSpotifyToken);

  // Initialize and sync connection state
  useEffect(() => {
    if (connectionStatus !== undefined) {
      setIsInitialized(true);
      
      if (connectionStatus.connected) {
        setConnectionState('connected');
        setProfile(connectionStatus.profile || null);
        setError(null);
      } else if (connectionStatus.needsRefresh) {
        setConnectionState('expired');
        setProfile(connectionStatus.profile || null);
      } else {
        setConnectionState('disconnected');
        setProfile(null);
      }
    }
  }, [connectionStatus]);

  // Auto-refresh expired tokens
  useEffect(() => {
    if (connectionState === 'expired' && user && !isSyncing) {
      console.log('Spotify token expired, attempting refresh...');
      handleTokenRefresh();
    }
  }, [connectionState, user, isSyncing]);

  const handleTokenRefresh = useCallback(async () => {
    if (!user || isSyncing) return false;

    try {
      setIsSyncing(true);
      setConnectionState('connecting');
      
      const result = await refreshToken({});
      
      if (result.success) {
        setConnectionState('connected');
        setError(null);
        console.log('Spotify token refreshed successfully');
        return true;
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Failed to refresh Spotify token:', error);
      setConnectionState('error');
      setError(error instanceof Error ? error.message : 'Failed to refresh token');
      return false;
    } finally {
      setIsSyncing(false);
    }
  }, [user, isSyncing, refreshToken]);

  const syncConnectionState = useCallback(async () => {
    if (!user || isSyncing) return Promise.resolve(false);

    try {
      setIsSyncing(true);
      setConnectionState('checking');
      console.log('Syncing Spotify connection state for user:', user.id);

      // The connection status is automatically updated via the useQuery hook
      // This function is mainly for manual refresh triggers
      
      return connectionStatus?.connected || false;
    } catch (error) {
      console.error('Error syncing Spotify connection:', error);
      setConnectionState('error');
      setError(error instanceof Error ? error.message : 'Failed to sync connection');
      return false;
    } finally {
      setIsSyncing(false);
    }
  }, [user, isSyncing, connectionStatus]);

  // Debounced refresh function with cooldown
  const refreshConnection = useCallback(
    debounce(async () => {
      const now = Date.now();
      if (!isSyncing && now - lastRefreshTime.current >= REFRESH_COOLDOWN) {
        console.log("🔄 Manually refreshing Spotify connection");
        lastRefreshTime.current = now;
        await syncConnectionState();
      }
    }, 1000),
    [syncConnectionState, isSyncing]
  );

  const handleConnect = async (): Promise<boolean> => {
    console.log('useSpotifyAuth: handleConnect called');
    
    if (!user) {
      console.log('useSpotifyAuth: No user found, cannot connect');
      toast({
        title: "Authentication Required",
        description: "Please sign in to connect your Spotify account.",
        variant: "destructive",
      });
      return false;
    }

    try {
      console.log('useSpotifyAuth: Setting connection state to connecting');
      setConnectionState('connecting');
      setError(null);
      
      console.log('useSpotifyAuth: Initiating Spotify OAuth flow');
      const result = await initiateAuth({});
      
      if (result.success && result.authUrl) {
        console.log('useSpotifyAuth: Redirecting to Spotify authorization');
        
        toast({
          title: "Redirecting to Spotify",
          description: "You'll be redirected to Spotify to authorize PlayBeg.",
        });

        // Redirect to Spotify OAuth
        window.location.href = result.authUrl;
        
        // Return true since we're redirecting (actual connection happens in callback)
        return true;
      } else {
        throw new Error('Failed to initiate Spotify authorization');
      }
    } catch (error) {
      console.error('useSpotifyAuth: Error connecting to Spotify:', error);
      setConnectionState('error');
      setError(error instanceof Error ? error.message : 'Failed to connect to Spotify');
      
      toast({
        title: "Connection Error",
        description: error instanceof Error ? error.message : 'Failed to connect to Spotify',
        variant: "destructive",
      });
      
      return false;
    }
  };

  const handleDisconnect = async (): Promise<boolean> => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to disconnect your Spotify account.",
        variant: "destructive",
      });
      return false;
    }

    try {
      setConnectionState('disconnected');
      setProfile(null);
      setError(null);
      
      await disconnectSpotify({});
      
      toast({
        title: "Disconnected",
        description: "Successfully disconnected from Spotify",
      });
      
      return true;
    } catch (error) {
      console.error('Error disconnecting from Spotify:', error);
      setError(error instanceof Error ? error.message : 'Failed to disconnect from Spotify');
      
      toast({
        title: "Disconnection Error",
        description: error instanceof Error ? error.message : 'Failed to disconnect from Spotify',
        variant: "destructive",
      });
      
      return false;
    }
  };

  // Define if user is authorized based on connection state
  const isAuthorized = connectionState === 'connected';

  return {
    connectionState,
    isAuthorized,
    profile,
    error,
    isInitialized,
    isSyncing,
    connectSpotify: handleConnect,
    disconnectSpotify: handleDisconnect,
    syncConnectionState: refreshConnection,
    refreshToken: handleTokenRefresh,
  };
}
