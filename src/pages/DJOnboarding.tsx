
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { motion } from "framer-motion";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import OnboardingRouter from "@/components/onboarding/OnboardingRouter";

const DJOnboarding = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Check if user needs onboarding using Convex
  const needsOnboarding = useQuery(api.onboarding.needsOnboarding, {});

  // Check if user should be on this page
  useEffect(() => {
    // If user has already completed onboarding, redirect to dashboard
    if (needsOnboarding === false) {
      navigate("/dashboard");
    }
  }, [needsOnboarding, navigate]);

  // Show loading state while checking onboarding status
  if (needsOnboarding === undefined) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
          className="h-10 w-10 border-4 border-t-purple-500 border-r-cyan-500 border-b-purple-500/30 border-l-cyan-500/30 rounded-full"
        />
      </div>
    );
  }

  // Show authentication required if no user
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-400 mb-6">Please sign in to continue with onboarding.</p>
          <button
            onClick={() => navigate("/auth")}
            className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200"
          >
            Go to Sign In
          </button>
        </div>
      </div>
    );
  }

  // Render the new onboarding router
  return <OnboardingRouter />;

};




export default DJOnboarding;
