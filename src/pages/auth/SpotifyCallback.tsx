import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { CheckCircle, AlertCircle, Loader2, Music } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * Spotify OAuth Callback Handler
 * 
 * This component handles the OAuth callback from Spotify after user authorization.
 * It exchanges the authorization code for access tokens and completes the connection.
 */

type CallbackState = 'processing' | 'success' | 'error';

interface CallbackError {
  message: string;
  details?: string;
  canRetry?: boolean;
}

const SpotifyCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [callbackState, setCallbackState] = useState<CallbackState>('processing');
  const [error, setError] = useState<CallbackError | null>(null);
  const [spotifyProfile, setSpotifyProfile] = useState<any>(null);

  const handleSpotifyCallback = useMutation(api.spotify_auth.handleSpotifyCallback);

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Check for authentication
        if (!user) {
          setError({
            message: 'Authentication required',
            details: 'Please sign in to connect your Spotify account',
            canRetry: false,
          });
          setCallbackState('error');
          return;
        }

        // Get OAuth parameters from URL
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        // Handle OAuth errors
        if (error) {
          let errorMessage = 'Spotify authorization failed';
          let errorDetails = '';

          switch (error) {
            case 'access_denied':
              errorMessage = 'Access denied';
              errorDetails = 'You declined to authorize PlayBeg with Spotify';
              break;
            case 'invalid_client':
              errorMessage = 'Configuration error';
              errorDetails = 'There was a problem with the Spotify integration setup';
              break;
            case 'invalid_grant':
              errorMessage = 'Authorization expired';
              errorDetails = 'The authorization request has expired. Please try again';
              break;
            case 'invalid_scope':
              errorMessage = 'Permission error';
              errorDetails = 'Required permissions are not available';
              break;
            default:
              errorDetails = `Spotify returned error: ${error}`;
          }

          setError({
            message: errorMessage,
            details: errorDetails,
            canRetry: error !== 'access_denied',
          });
          setCallbackState('error');
          return;
        }

        // Validate required parameters
        if (!code || !state) {
          setError({
            message: 'Invalid callback parameters',
            details: 'Missing authorization code or state parameter',
            canRetry: true,
          });
          setCallbackState('error');
          return;
        }

        // Exchange code for tokens
        console.log('Processing Spotify OAuth callback...');
        const result = await handleSpotifyCallback({ code, state });

        if (result.success) {
          setSpotifyProfile(result.profile);
          setCallbackState('success');
          
          toast({
            title: "Spotify Connected!",
            description: `Successfully connected to ${result.profile.displayName || result.profile.email}`,
          });

          // Redirect back to onboarding after a short delay
          setTimeout(() => {
            navigate('/dj-onboarding', { replace: true });
          }, 2000);
        } else {
          throw new Error('Callback processing failed');
        }

      } catch (error: any) {
        console.error('Spotify callback error:', error);
        
        let errorMessage = 'Connection failed';
        let errorDetails = error.message || 'An unexpected error occurred';
        let canRetry = true;

        // Handle specific error types
        if (error.message.includes('Invalid or expired OAuth state')) {
          errorMessage = 'Security verification failed';
          errorDetails = 'The authorization request has expired or been tampered with';
          canRetry = true;
        } else if (error.message.includes('Token exchange failed')) {
          errorMessage = 'Token exchange failed';
          errorDetails = 'Failed to exchange authorization code for access tokens';
          canRetry = true;
        } else if (error.message.includes('Failed to fetch Spotify profile')) {
          errorMessage = 'Profile access failed';
          errorDetails = 'Connected to Spotify but failed to retrieve profile information';
          canRetry = false;
        }

        setError({
          message: errorMessage,
          details: errorDetails,
          canRetry,
        });
        setCallbackState('error');

        toast({
          title: "Connection Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    };

    processCallback();
  }, [searchParams, user, handleSpotifyCallback, navigate, toast]);

  const handleRetry = () => {
    navigate('/dj-onboarding', { replace: true });
  };

  const handleContinueWithoutSpotify = () => {
    navigate('/dj-onboarding', { replace: true });
  };

  const renderProcessingState = () => (
    <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md w-full max-w-lg">
      <CardHeader>
        <CardTitle className="text-2xl text-white text-center">
          Connecting to Spotify
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col items-center space-y-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
            className="h-16 w-16 border-4 border-t-purple-500 border-r-cyan-500 border-b-purple-500/30 border-l-cyan-500/30 rounded-full"
          />
          <div className="text-center">
            <p className="text-white font-medium">Processing authorization...</p>
            <p className="text-gray-400 text-sm mt-1">
              Exchanging tokens and setting up your connection
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderSuccessState = () => (
    <Card className="bg-black/50 border-green-500/20 backdrop-blur-md w-full max-w-lg">
      <CardHeader>
        <CardTitle className="text-2xl text-white text-center">
          Spotify Connected!
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col items-center space-y-4">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200, damping: 10 }}
            className="h-16 w-16 bg-green-500/20 rounded-full flex items-center justify-center"
          >
            <CheckCircle className="h-8 w-8 text-green-500" />
          </motion.div>
          
          <div className="text-center">
            <p className="text-white font-medium">Successfully connected!</p>
            {spotifyProfile && (
              <p className="text-gray-400 text-sm mt-1">
                Connected as {spotifyProfile.displayName || spotifyProfile.email}
              </p>
            )}
          </div>

          <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/20 w-full">
            <div className="flex items-center space-x-3">
              <Music className="h-5 w-5 text-green-500" />
              <div>
                <h4 className="text-green-400 font-medium">Ready to go!</h4>
                <p className="text-green-300 text-sm">
                  Taking you back to complete your onboarding...
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderErrorState = () => (
    <Card className="bg-black/50 border-red-500/20 backdrop-blur-md w-full max-w-lg">
      <CardHeader>
        <CardTitle className="text-2xl text-white text-center">
          Connection Failed
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-16 w-16 bg-red-500/20 rounded-full flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-red-500" />
          </div>
          
          <div className="text-center">
            <p className="text-white font-medium">{error?.message}</p>
            {error?.details && (
              <p className="text-gray-400 text-sm mt-1">{error.details}</p>
            )}
          </div>

          <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/20 w-full">
            <div className="text-center">
              <h4 className="text-red-400 font-medium mb-2">What can you do?</h4>
              <ul className="text-red-300 text-sm space-y-1">
                <li>• Check your Spotify account is active</li>
                <li>• Ensure you have a stable internet connection</li>
                <li>• Try the connection process again</li>
                <li>• Continue with manual entry for now</li>
              </ul>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 w-full">
            {error?.canRetry && (
              <Button
                onClick={handleRetry}
                className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 text-white"
              >
                Try Again
              </Button>
            )}
            <Button
              onClick={handleContinueWithoutSpotify}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Continue Without Spotify
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full flex justify-center"
      >
        {callbackState === 'processing' && renderProcessingState()}
        {callbackState === 'success' && renderSuccessState()}
        {callbackState === 'error' && renderErrorState()}
      </motion.div>
    </div>
  );
};

export default SpotifyCallback;
