/**
 * Tests for Spotify OAuth Callback Handler
 * 
 * This test file validates the Spotify OAuth callback processing,
 * error handling, and user experience during the authorization flow.
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { ConvexProvider } from 'convex/react';
import { ConvexReactClient } from 'convex/react';
import SpotifyCallback from '../SpotifyCallback';

// Mock Convex client
const mockConvexClient = new ConvexReactClient('mock-url');

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useSearchParams: () => [new URLSearchParams()],
  };
});

// Mock Convex mutations
const mockHandleSpotifyCallback = vi.fn();
vi.mock('convex/react', async () => {
  const actual = await vi.importActual('convex/react');
  return {
    ...actual,
    useMutation: () => mockHandleSpotifyCallback,
  };
});

// Mock auth context
vi.mock('@/context/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id', email: '<EMAIL>' },
  }),
}));

// Mock toast hook
const mockToast = vi.fn();
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: mockToast,
  }),
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConvexProvider client={mockConvexClient}>
    <BrowserRouter>
      <div className="min-h-screen bg-gray-900">
        {children}
      </div>
    </BrowserRouter>
  </ConvexProvider>
);

describe('SpotifyCallback', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();
    mockToast.mockClear();
    mockHandleSpotifyCallback.mockClear();
  });

  describe('Processing State', () => {
    it('shows processing state initially', () => {
      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      expect(screen.getByText('Connecting to Spotify')).toBeInTheDocument();
      expect(screen.getByText('Processing authorization...')).toBeInTheDocument();
    });

    it('shows loading spinner during processing', () => {
      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      // Should have animated loading spinner
      const spinner = document.querySelector('[class*="animate"]');
      expect(spinner).toBeInTheDocument();
    });
  });

  describe('Success State', () => {
    it('shows success state after successful connection', async () => {
      // Mock successful callback
      mockHandleSpotifyCallback.mockResolvedValue({
        success: true,
        profile: {
          id: 'spotify-user-id',
          displayName: 'Test User',
          email: '<EMAIL>',
        },
      });

      // Mock URL params with successful OAuth response
      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('code=test-code&state=test-state'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Spotify Connected!')).toBeInTheDocument();
      });

      expect(screen.getByText('Successfully connected!')).toBeInTheDocument();
      expect(screen.getByText('Connected as Test User')).toBeInTheDocument();
    });

    it('shows success toast notification', async () => {
      mockHandleSpotifyCallback.mockResolvedValue({
        success: true,
        profile: {
          id: 'spotify-user-id',
          displayName: 'Test User',
          email: '<EMAIL>',
        },
      });

      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('code=test-code&state=test-state'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: "Spotify Connected!",
          description: "Successfully connected to Test User",
        });
      });
    });

    it('redirects to onboarding after successful connection', async () => {
      mockHandleSpotifyCallback.mockResolvedValue({
        success: true,
        profile: {
          id: 'spotify-user-id',
          displayName: 'Test User',
          email: '<EMAIL>',
        },
      });

      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('code=test-code&state=test-state'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      // Should redirect after 2 seconds
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/dj-onboarding', { replace: true });
      }, { timeout: 3000 });
    });
  });

  describe('Error States', () => {
    it('handles OAuth access denied error', async () => {
      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('error=access_denied'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Connection Failed')).toBeInTheDocument();
      });

      expect(screen.getByText('Access denied')).toBeInTheDocument();
      expect(screen.getByText('You declined to authorize PlayBeg with Spotify')).toBeInTheDocument();
    });

    it('handles invalid client error', async () => {
      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('error=invalid_client'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Configuration error')).toBeInTheDocument();
      });
    });

    it('handles missing authorization code', async () => {
      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('state=test-state'), // Missing code parameter
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Invalid callback parameters')).toBeInTheDocument();
      });
    });

    it('handles token exchange failure', async () => {
      mockHandleSpotifyCallback.mockRejectedValue(new Error('Token exchange failed'));

      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('code=test-code&state=test-state'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Connection Failed')).toBeInTheDocument();
      });

      expect(screen.getByText('Token exchange failed')).toBeInTheDocument();
    });

    it('shows retry button for retryable errors', async () => {
      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('error=invalid_grant'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Try Again')).toBeInTheDocument();
      });
    });

    it('does not show retry button for access denied', async () => {
      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('error=access_denied'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.queryByText('Try Again')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Continue Without Spotify')).toBeInTheDocument();
    });
  });

  describe('Authentication Requirements', () => {
    it('shows authentication error when user is not signed in', async () => {
      // Mock no user
      vi.mocked(require('@/context/AuthContext').useAuth).mockReturnValue({
        user: null,
      });

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Authentication required')).toBeInTheDocument();
      });
    });
  });

  describe('User Experience', () => {
    it('provides helpful error messages and recovery options', async () => {
      vi.mocked(require('react-router-dom').useSearchParams).mockReturnValue([
        new URLSearchParams('error=invalid_scope'),
      ]);

      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('What can you do?')).toBeInTheDocument();
      });

      expect(screen.getByText(/Check your Spotify account is active/)).toBeInTheDocument();
      expect(screen.getByText(/Try the connection process again/)).toBeInTheDocument();
      expect(screen.getByText(/Continue with manual entry for now/)).toBeInTheDocument();
    });

    it('maintains consistent branding and design', () => {
      render(
        <TestWrapper>
          <SpotifyCallback />
        </TestWrapper>
      );

      // Should have gradient background
      const container = document.querySelector('.bg-gradient-to-br');
      expect(container).toBeInTheDocument();

      // Should have purple/blue theme colors
      const purpleElements = document.querySelectorAll('[class*="purple"]');
      expect(purpleElements.length).toBeGreaterThan(0);
    });
  });
});

export default {};
