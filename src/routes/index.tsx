
import { createBrowserRouter } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import ProtectedRoute from '@/routes/ProtectedRoute';
import { AuthProvider } from '@/context/AuthContext';
import Home from '@/pages/Home';
import Dashboard from '@/pages/Dashboard';
import Login from '@/pages/auth/Login';
import Signup from '@/pages/auth/Signup';
import ResetPassword from '@/pages/auth/ResetPassword';
import UpdatePassword from '@/pages/auth/UpdatePassword';
import NotFound from '@/pages/NotFound';
import About from '@/pages/About';
import PrivacyPolicy from '@/pages/PrivacyPolicy';
import Terms from '@/pages/Terms';
import CookiePolicy from '@/pages/CookiePolicy';
import FAQ from '@/pages/FAQ';
import SongRequest from '@/pages/SongRequest';
import DJOnboarding from '@/pages/DJOnboarding';
import SpotifyCallback from '@/pages/auth/SpotifyCallback';
import BlogIndex from '@/pages/blog/BlogIndex';
import BlogPost from '@/pages/blog/BlogPost';
import BlogCategory from '@/pages/blog/BlogCategory';
import BlogTag from '@/pages/blog/BlogTag';
import Partner from '@/pages/Partner';
import ProductRoadmap from '@/pages/ProductRoadmap';

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthProvider>
        <MainLayout />
      </AuthProvider>
    ),
    children: [
      {
        path: '/',
        element: <Home />,
      },
      {
        path: '/dashboard',
        element: <ProtectedRoute />,
        children: [
          {
            path: '',
            element: <Dashboard />
          }
        ]
      },
      {
        path: '/login',
        element: <Login />,
      },
      {
        path: '/signup',
        element: <Signup />,
      },
      {
        path: '/auth/reset-password',
        element: <ResetPassword />,
      },
      {
        path: '/auth/update-password',
        element: <UpdatePassword />,
      },
      {
        path: '/auth/spotify-callback',
        element: <ProtectedRoute />,
        children: [
          {
            path: '',
            element: <SpotifyCallback />
          }
        ]
      },
      {
        path: '/about',
        element: <About />,
      },
      {
        path: '/privacy-policy',
        element: <PrivacyPolicy />,
      },
      {
        path: '/terms',
        element: <Terms />,
      },
      {
        path: '/cookie-policy',
        element: <CookiePolicy />,
      },
      {
        path: '/faq',
        element: <FAQ />,
      },
      {
        path: '/request',
        element: <SongRequest />,
      },
      {
        path: '/onboarding',
        element: <ProtectedRoute />,
        children: [
          {
            path: '',
            element: <DJOnboarding />
          }
        ]
      },
      {
        path: '/blog',
        element: <BlogIndex />,
      },
      {
        path: '/blog/category/:category',
        element: <BlogCategory />,
      },
      {
        path: '/blog/tag/:tag',
        element: <BlogTag />,
      },
      {
        path: '/blog/:slug',
        element: <BlogPost />,
      },
      {
        path: '/partner',
        element: <Partner />,
      },
      {
        path: '/roadmap',
        element: <ProductRoadmap />,
      },
      {
        path: '*',
        element: <NotFound />,
      },
    ],
  },
]);

export default router;
