import { ConvexReactClient } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface TokenResponse {
  token: string;
  source: string;
  expiresIn?: number;
}

/**
 * Service for retrieving Apple Music tokens using Convex
 * This replaces the Supabase Edge Function approach with pure Convex integration
 */
export const AppleMusicTokenService = {
  // Simple in-memory cache
  _tokenCache: new Map<string, { token: string; expires: number }>(),
  _TOKEN_CACHE_DURATION: 5 * 60 * 1000, // 5 minutes in milliseconds
  _convexClient: null as ConvexReactClient | null,

  /**
   * Initialize the service with a Convex client
   */
  init(convexClient: ConvexReactClient) {
    this._convexClient = convexClient;
  },

  /**
   * Get Apple Music token for a session
   * @param sessionId The session ID
   * @returns Promise with the token
   */
  async getTokenForSession(sessionId: string): Promise<string> {
    try {
      // Check cache first
      const cacheKey = `session:${sessionId}`;
      const cachedToken = this._tokenCache.get(cacheKey);

      if (cachedToken && cachedToken.expires > Date.now()) {
        console.log('Using cached token for session:', sessionId);
        return cachedToken.token;
      }

      console.log('Fetching token for session:', sessionId);

      // For now, session-specific tokens fall back to developer token
      // In the future, this could be enhanced to support session-specific tokens via Convex
      console.log('Using developer token for session (Convex migration)');
      const developerToken = await this.getDeveloperToken();

      // Cache the token
      this._tokenCache.set(cacheKey, {
        token: developerToken,
        expires: Date.now() + this._TOKEN_CACHE_DURATION
      });

      return developerToken;
    } catch (error) {
      console.error('Error getting token for session:', error);
      // Fall back to developer token as a last resort
      try {
        return this.getDeveloperToken();
      } catch (devTokenError) {
        console.error('Failed to get developer token as fallback:', devTokenError);
        throw error; // Throw the original error if we can't get the developer token
      }
    }
  },

  /**
   * Get Apple Music token for the current authenticated user
   * @returns Promise with the token
   */
  async getTokenForCurrentUser(): Promise<string> {
    try {
      // Check cache first
      const cacheKey = 'currentUser';
      const cachedToken = this._tokenCache.get(cacheKey);

      if (cachedToken && cachedToken.expires > Date.now()) {
        console.log('Using cached token for current user');
        return cachedToken.token;
      }

      console.log('Fetching token for current user');

      // For now, user-specific tokens fall back to developer token
      // In the future, this could be enhanced to support user-specific tokens via Convex
      console.log('Using developer token for current user (Convex migration)');
      const developerToken = await this.getDeveloperToken();

      // Cache the token
      this._tokenCache.set(cacheKey, {
        token: developerToken,
        expires: Date.now() + this._TOKEN_CACHE_DURATION
      });

      return developerToken;
    } catch (error) {
      console.error('Error getting token for current user:', error);
      console.warn('Falling back to developer token due to error');
      return this.getDeveloperToken();
    }
  },

  /**
   * Get the Apple Music Developer Token from Convex
   * @returns Promise with the developer token
   */
  async getDeveloperToken(): Promise<string> {
    try {
      // Check cache first
      const cacheKey = 'developerToken';
      const cachedToken = this._tokenCache.get(cacheKey);

      if (cachedToken && cachedToken.expires > Date.now()) {
        console.log('Using cached developer token');
        return cachedToken.token;
      }

      console.log('Fetching Apple Music Developer Token from Convex');

      if (!this._convexClient) {
        throw new Error('Convex client not initialized. Call AppleMusicTokenService.init() first.');
      }

      // Call the Convex query to get the developer token
      const tokenData = await this._convexClient.query(api.appleMusicTokens.getAppleMusicDeveloperToken, {});

      if (!tokenData.token) {
        throw new Error('No developer token returned from Convex');
      }

      // Cache the token with a longer expiration (1 hour)
      this._tokenCache.set(cacheKey, {
        token: tokenData.token,
        expires: Date.now() + (60 * 60 * 1000) // 1 hour
      });

      console.log('Successfully retrieved Apple Music Developer Token from Convex');
      return tokenData.token;
    } catch (error) {
      console.error('Error getting developer token from Convex:', error);
      throw error;
    }
  },

  /**
   * Clear the token cache
   */
  clearCache(): void {
    this._tokenCache.clear();
  }
};
