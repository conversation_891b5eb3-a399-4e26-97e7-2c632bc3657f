/**
 * Tests for Apple Music Token Service - Convex Migration
 * 
 * This test file validates that the Apple Music integration has been
 * successfully migrated from Supabase to Convex architecture.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AppleMusicTokenService } from '../AppleMusicTokenService';

// Mock Convex client
const mockConvexClient = {
  query: vi.fn(),
};

// Mock Convex query response
const mockTokenResponse = {
  token: 'eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.test.token',
  source: 'convex-environment',
  expiresIn: 15552000,
};

describe('AppleMusicTokenService - Convex Migration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    AppleMusicTokenService.clearCache();
    
    // Initialize with mock Convex client
    AppleMusicTokenService.init(mockConvexClient as any);
  });

  describe('Convex Integration', () => {
    it('should initialize with Convex client', () => {
      expect(() => {
        AppleMusicTokenService.init(mockConvexClient as any);
      }).not.toThrow();
    });

    it('should throw error if not initialized', async () => {
      // Create a fresh service instance without initialization
      const freshService = { ...AppleMusicTokenService };
      freshService._convexClient = null;

      await expect(freshService.getDeveloperToken()).rejects.toThrow(
        'Convex client not initialized'
      );
    });

    it('should retrieve developer token from Convex', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      const token = await AppleMusicTokenService.getDeveloperToken();

      expect(mockConvexClient.query).toHaveBeenCalledWith(
        expect.objectContaining({
          // Should call the Convex query
        }),
        {}
      );
      expect(token).toBe(mockTokenResponse.token);
    });

    it('should cache developer token', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      // First call
      const token1 = await AppleMusicTokenService.getDeveloperToken();
      
      // Second call (should use cache)
      const token2 = await AppleMusicTokenService.getDeveloperToken();

      expect(token1).toBe(token2);
      expect(mockConvexClient.query).toHaveBeenCalledTimes(1); // Only called once due to caching
    });

    it('should handle Convex query errors', async () => {
      const error = new Error('Convex query failed');
      mockConvexClient.query.mockRejectedValue(error);

      await expect(AppleMusicTokenService.getDeveloperToken()).rejects.toThrow(
        'Convex query failed'
      );
    });

    it('should handle missing token in response', async () => {
      mockConvexClient.query.mockResolvedValue({ token: null });

      await expect(AppleMusicTokenService.getDeveloperToken()).rejects.toThrow(
        'No developer token returned from Convex'
      );
    });
  });

  describe('Session Token Fallback', () => {
    it('should use developer token for session-specific requests', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      const token = await AppleMusicTokenService.getTokenForSession('test-session');

      expect(token).toBe(mockTokenResponse.token);
      expect(mockConvexClient.query).toHaveBeenCalled();
    });

    it('should cache session tokens', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      const token1 = await AppleMusicTokenService.getTokenForSession('test-session');
      const token2 = await AppleMusicTokenService.getTokenForSession('test-session');

      expect(token1).toBe(token2);
      expect(mockConvexClient.query).toHaveBeenCalledTimes(1);
    });
  });

  describe('Current User Token Fallback', () => {
    it('should use developer token for current user requests', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      const token = await AppleMusicTokenService.getTokenForCurrentUser();

      expect(token).toBe(mockTokenResponse.token);
      expect(mockConvexClient.query).toHaveBeenCalled();
    });

    it('should cache current user tokens', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      const token1 = await AppleMusicTokenService.getTokenForCurrentUser();
      const token2 = await AppleMusicTokenService.getTokenForCurrentUser();

      expect(token1).toBe(token2);
      expect(mockConvexClient.query).toHaveBeenCalledTimes(1);
    });
  });

  describe('Cache Management', () => {
    it('should clear cache when requested', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      // Get token (should cache)
      await AppleMusicTokenService.getDeveloperToken();
      
      // Clear cache
      AppleMusicTokenService.clearCache();
      
      // Get token again (should call Convex again)
      await AppleMusicTokenService.getDeveloperToken();

      expect(mockConvexClient.query).toHaveBeenCalledTimes(2);
    });

    it('should respect cache expiration', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      // Mock Date.now to control cache expiration
      const originalDateNow = Date.now;
      let currentTime = 1000000;
      Date.now = vi.fn(() => currentTime);

      try {
        // Get token (should cache)
        await AppleMusicTokenService.getDeveloperToken();
        
        // Advance time beyond cache duration (1 hour + 1 minute)
        currentTime += (60 * 60 * 1000) + (60 * 1000);
        
        // Get token again (should call Convex again due to expiration)
        await AppleMusicTokenService.getDeveloperToken();

        expect(mockConvexClient.query).toHaveBeenCalledTimes(2);
      } finally {
        Date.now = originalDateNow;
      }
    });
  });

  describe('Architecture Verification', () => {
    it('should not contain any Supabase references', () => {
      const serviceCode = AppleMusicTokenService.toString();
      
      // Verify no Supabase references
      expect(serviceCode).not.toContain('supabase');
      expect(serviceCode).not.toContain('edge-function');
      expect(serviceCode).not.toContain('functions.url');
    });

    it('should use Convex query for token retrieval', async () => {
      mockConvexClient.query.mockResolvedValue(mockTokenResponse);

      await AppleMusicTokenService.getDeveloperToken();

      // Verify it calls Convex query, not HTTP fetch
      expect(mockConvexClient.query).toHaveBeenCalled();
      
      // Verify no fetch calls were made
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should follow the same pattern as Spotify integration', () => {
      // Both services should have similar initialization patterns
      expect(typeof AppleMusicTokenService.init).toBe('function');
      expect(typeof AppleMusicTokenService.clearCache).toBe('function');
      
      // Both should use Convex client
      expect(AppleMusicTokenService._convexClient).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should provide clear error messages for missing environment variables', async () => {
      mockConvexClient.query.mockRejectedValue(
        new Error('Apple Music Developer Token not configured in Convex environment')
      );

      await expect(AppleMusicTokenService.getDeveloperToken()).rejects.toThrow(
        'Apple Music Developer Token not configured in Convex environment'
      );
    });

    it('should handle network errors gracefully', async () => {
      mockConvexClient.query.mockRejectedValue(new Error('Network error'));

      await expect(AppleMusicTokenService.getDeveloperToken()).rejects.toThrow(
        'Network error'
      );
    });
  });
});

export default {};
